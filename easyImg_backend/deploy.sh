#!/bin/bash

# 部署脚本
echo "开始部署..."

# 1. 运行npm run build
echo "步骤1: 运行npm run build..."
npm run build

if [ $? -ne 0 ]; then
    echo "构建失败，退出部署"
    exit 1
fi

# 2. 压缩dist文件为zip
echo "步骤2: 压缩dist文件..."
zip -r dist.zip dist/

if [ $? -ne 0 ]; then
    echo "压缩失败，退出部署"
    exit 1
fi

# 3. 上传zip到服务器
echo "步骤3: 上传zip文件到服务器..."
scp dist.zip root@*************:~/

if [ $? -ne 0 ]; then
    echo "上传失败，退出部署"
    exit 1
fi

# 4. 登录服务器并执行部署操作
echo "步骤4: 登录服务器执行部署..."
ssh root@************* << 'EOF'
    # 解压上传的zip文件
    echo "解压zip文件..."
    unzip -o ~/dist.zip -d ~/
    
    # 将解压后的文件夹移动到/home/<USER>
    echo "移动文件到/home/<USER>/easyImg..."
    mv ~/dist /home/<USER>/easyImg
    
    # 删除zip文件
    echo "删除zip文件..."
    rm ~/dist.zip
    
    # cd到/home/<USER>
    cd /home/<USER>/easyImg
    
    # 将解压后的文件中的所有内容移动到backend下，有冲突则覆盖
    echo "移动文件到backend目录..."
    cp -rf dist/* backend/
    
    # 删除dist文件夹
    rm -rf dist
    
    echo "部署完成！"
EOF

# 5. 删除本地zip文件
echo "步骤5: 清理本地文件..."
rm dist.zip

echo "所有部署步骤完成！"


