import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ImglistModule } from './imglist/imglist.module';
import { ImgDetailModule } from './imgDetail/imgDetail.module';
import { SearchModule } from './search/search.module';

@Module({
	imports: [ImglistModule, ImgDetailModule, SearchModule],
	controllers: [AppController],
	providers: [AppService],
})
export class AppModule {}
