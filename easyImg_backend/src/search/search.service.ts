import { Injectable } from '@nestjs/common';
import axios from 'axios';
import * as https from 'https';
import { getAlbumList } from 'src/tools/getAlbumLIst';
import { ReqErr, ReqSucc } from 'src/tools/ReqErr';

const agent = new https.Agent({
	rejectUnauthorized: false,
});

const searchUrl = (page: number, searchId: string) => {
	return `https://meitu001.cc/e/search/result/index.php?page=${page - 1}&searchid=${searchId}`
}
const searchIdUrl = 'https://meitu001.cc/e/search/'

@Injectable()
export class SearchService {
	async search(searchId: string, page: number) {
		try {
			const response = await axios.get(searchUrl(page, searchId), {
				httpsAgent: agent,
			});

			const imgList = getAlbumList(response.data as string);

			return new ReqSucc(200, {
				list: imgList,
				hasMore: imgList.length !== 0,
			});
		} catch (error) {
			console.error(error);
			return new ReqErr(500, '获取图片列表失败');
		}
	}

	async getSearchId(keyword: string) {
		try {
			const formData = new URLSearchParams();
			formData.append('tbname', 'news');
			formData.append('show', 'title');
			formData.append('tempid', '1');
			formData.append('keyboard', keyword);

			const response = await axios.post(searchIdUrl, formData, {
				httpsAgent: agent,
			});
			const path: string = (response.request as unknown as { path: string }).path;
			const searchidObj = searchIdToObj(path);
			return new ReqSucc(200, {
				searchId: searchidObj.searchid,
			});

		} catch (error) {
			console.error(error);
			return new ReqSucc(200, {
				searchId: '',
			});
		}
	}
}

function searchIdToObj(path: string): { [key: string]: string } {
	const searchid = path.split('?')[1];
	const searchidObj = {};
	searchid.split('&').forEach(item => {
		const [key, value] = item.split('=');
		searchidObj[key] = value;
	});
	return searchidObj;
}