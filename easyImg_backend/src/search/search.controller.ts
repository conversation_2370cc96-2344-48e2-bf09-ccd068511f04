import { Controller, Get, Header, Query } from '@nestjs/common';
import { SearchService } from './search.service';

@Controller('search')
export class SearchController {
	constructor(private readonly searchService: SearchService) { }

	@Get()
	@Header('Access-Control-Allow-Origin', '*')
	@Header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
	@Header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
	search(@Query('searchId') searchId: string, @Query('page') page: number) {
		return this.searchService.search(searchId, page);
	}


	@Get('getSearchId')
	@Header('Access-Control-Allow-Origin', '*')
	@Header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
	@Header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
	getSearchId(@Query('keyword') keyword: string) {
		return this.searchService.getSearchId(keyword);
	}
}
