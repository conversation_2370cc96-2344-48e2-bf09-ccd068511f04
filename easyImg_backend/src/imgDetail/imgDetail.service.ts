import { Injectable } from '@nestjs/common';
import axios from 'axios';
import * as https from 'https';
import * as cheerio from 'cheerio';
import { ReqErr, ReqSucc } from 'src/tools/ReqErr';

const agent = new https.Agent({
	rejectUnauthorized: false,
});

// 全部图片爬虫网址
const ImgDetailUrl = (id: number) => `https://meitu001.cc/photo/${id}.html`;

@Injectable()
export class ImgDetailService {
	async getImgUrl(imgPageUrlArr: string[]) {
		try {
			const proArr: Promise<string>[] = [];
			for (let i = 0; i < imgPageUrlArr.length; i++) {
				const currentPro = axios
					.get(imgPageUrlArr[i], { httpsAgent: agent })
					.then((response) => {
						const $ = cheerio.load(response.data as string);
						const imgUrl =
							$('div.content').find('img').attr('src') || '';
						return imgUrl;
					});
				proArr.push(currentPro);
			}
			const res = await Promise.all(proArr);
			return res;
		} catch (error) {
			console.error(error);
			throw new ReqErr(500, '获取图片地址失败');
		}
	}

	async findOne(id: number) {
		try {
			const url = ImgDetailUrl(id);
			const response = await axios.get(url, { httpsAgent: agent });
			const $ = cheerio.load(response.data as string);

			// 获取标题
			const title = $('div.album-top.card').find('h1').text();

			// 获取图片数量
			const listElList = $('div.album-top.card').find('dd');
			const imgCount = parseInt($(listElList[3]).text());

			// 获取图片页面地址
			const imgPageUrlList: string[] = [];
			for (let i = 1; i <= imgCount; i++) {
				if (i === 1) {
					imgPageUrlList.push(`https://meitu001.cc/photo/${id}.html`);
				} else {
					imgPageUrlList.push(
						`https://meitu001.cc/photo/${id}_${i}.html`,
					);
				}
			}
			const imgUrlList: string[] = await this.getImgUrl(imgPageUrlList);

			return new ReqSucc(200, {
				imgUrlList,
				total: imgCount,
				title,
			});
		} catch (error) {
			console.error(error);
			if (error instanceof ReqErr) {
				return error;
			} else {
				return new ReqErr(500, '获取图片地址失败');
			}
		}
	}
}
