import {
	Controller,
	Get,
	Body,
	Param,
	Header,
} from '@nestjs/common';
import { ImgDetailService } from './imgDetail.service';
@Controller('imgDetail')
export class ImgDetailController {
	constructor(private readonly imgDetailService: ImgDetailService) { }

	@Get(':id')
	@Header('Access-Control-Allow-Origin', '*')
	@Header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
	@Header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
	findOne(@Param('id') id: string) {
		return this.imgDetailService.findOne(+id);
	}
}
