import { Logger } from '@nestjs/common';

export function LogMethod() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {

    const originalMethod: () => any = descriptor.value as () => any;
    const logger = new Logger(target.constructor.name);

    descriptor.value = function (...args: any[]) {
      const startTime = Date.now();
      const methodName = propertyKey;
      const className = target.constructor.name;
      
      // 记录方法调用开始 - 添加明显的标识
      console.log(`🚀 [${className}.${methodName}] 方法调用开始 - 参数: ${JSON.stringify(args)}`);
      logger.log(`🚀 [${className}.${methodName}] 方法调用开始 - 参数: ${JSON.stringify(args)}`);
      
      try {
        // 执行原始方法
        const result = originalMethod.apply(this, args);
        
        // 如果是 Promise，处理异步结果
        if (result instanceof Promise) {
          return result
            .then((res: any) => {
              const endTime = Date.now();
              const duration = endTime - startTime;
              console.log(`✅ [${className}.${methodName}] 方法调用成功 - 耗时: ${duration}ms`);
              logger.log(`✅ [${className}.${methodName}] 方法调用成功 - 耗时: ${duration}ms`);
              return res;
            })
            .catch((error: any) => {
              const endTime = Date.now();
              const duration = endTime - startTime;
              const errorMessage = error?.message || String(error);
              console.error(`❌ [${className}.${methodName}] 方法调用失败 - 耗时: ${duration}ms - 错误: ${errorMessage}`);
              logger.error(`❌ [${className}.${methodName}] 方法调用失败 - 耗时: ${duration}ms - 错误: ${errorMessage}`);
              throw error;
            });
        } else {
          // 同步方法
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`✅ [${className}.${methodName}] 方法调用成功 - 耗时: ${duration}ms`);
          logger.log(`✅ [${className}.${methodName}] 方法调用成功 - 耗时: ${duration}ms`);
          return result;
        }
      } catch (error: any) {
        // 同步方法异常处理
        const endTime = Date.now();
        const duration = endTime - startTime;
        const errorMessage = error?.message || String(error);
        console.error(`❌ [${className}.${methodName}] 方法调用失败 - 耗时: ${duration}ms - 错误: ${errorMessage}`);
        logger.error(`❌ [${className}.${methodName}] 方法调用失败 - 耗时: ${duration}ms - 错误: ${errorMessage}`);
        throw error;
      }
    };

    return descriptor;
  };
}
