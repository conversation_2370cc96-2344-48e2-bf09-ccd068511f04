import * as cheerio from 'cheerio';
import { URL } from 'url';

export type TagItem = {
	tagName: string;
	tagId: string;
};

export type ImgItem = {
	coverImg: string;
	title: string;
	id: number;
	modelList: TagItem[];
	tagList: TagItem[];
};

export const getAlbumList = (html: string) => {
	const $ = cheerio.load(html);
	// 所有的li标签
	const listElArr = $('div.list.container').find('ul').children();

	// 可用的图片列表
	const imgList: ImgItem[] = [];

	// 遍历li标签
	listElArr.each((index, element) => {
		const imgItem: ImgItem = {
			coverImg: '',
			title: '',
			id: 0,
			modelList: [],
			tagList: [],
		};

		// 获取封面
		const imgUrl = $(element).find('img').attr('data-src');
		if (imgUrl) {
			const url = new URL(imgUrl);
			const coverImg = url.searchParams.get('src')!;
			if (!coverImg) {
				imgItem.coverImg = imgUrl;
			} else {
				imgItem.coverImg = coverImg;
			}
		}

		// 获取标题和连接
		const title = $(element).find('.list-body').find('a')[0];
		if (title) {
			imgItem.title = $(title.children[0]).text();
			const href = title.attribs.href;
			if (href.startsWith('/')) {
				imgItem.id = parseInt(
					title.attribs.href.split('/')[2].split('.')[0],
				);
			} else {
				imgItem.id = 0;
			}
		}
		// 获取模特名称
		const modelName = $(element).find('.list-body').find('dd')[1];
		if (modelName) {
			const a = $(modelName).find('a');
			a.each((index, element) => {
				const tagName = $(element).text();
				const tagId = $(element).attr('href')!.split('/')[2];
				imgItem.modelList.push({ tagName, tagId });
			});
		}
		// 获取标签
		const tagList = $(element).find('.list-body').find('dd')[2];
		if (tagList) {
			const a = $(tagList).find('a');
			a.each((index, element) => {
				const tagName = $(element).text();
				const tagId = $(element).attr('href')!.split('/')[2];
				imgItem.tagList.push({ tagName, tagId });
			});
		}
		// 过滤可用
		if (imgItem.id) {
			imgList.push(imgItem);
		}
	});

	return imgList;
};
