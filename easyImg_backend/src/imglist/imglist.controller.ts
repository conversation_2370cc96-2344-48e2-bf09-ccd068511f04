import { Controller, Get, Param, Headers, Header, Query } from '@nestjs/common';
import { ImglistService } from './imglist.service';
import { LogMethod } from '../tools/logger.decorator';

@Controller('imgList')
export class ImglistController {
	constructor(private readonly imglistService: ImglistService) {}

	@Get('all')
	@Header('Access-Control-Allow-Origin', '*')
	@Header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
	@Header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
	@LogMethod()
	getImgList(@Query('page') page: number) {
		return this.imglistService.getImgList(+page);
	}

	@Get('hotTags')
	@Header('Access-Control-Allow-Origin', '*')
	@Header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
	@Header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
	getImgTypeList() {
		return this.imglistService.getImgTypeList();
	}

	@Get('getImgListByType/:type')
	@Header('Access-Control-Allow-Origin', '*')
	@Header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
	@Header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
	getImgListByType(@Param('type') type: string, @Query('page') page: number) {
		return this.imglistService.getImgListByType(type, +page);
	}
}
