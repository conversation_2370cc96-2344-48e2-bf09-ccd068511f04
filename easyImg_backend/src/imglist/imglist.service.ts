import { Injectable } from '@nestjs/common';
import axios from 'axios';
import * as https from 'https';
import { ReqErr, ReqSucc } from 'src/tools/ReqErr';
import { getAlbumList } from 'src/tools/getAlbumLIst';

// 全部图片爬虫网址
const getImgListUrl = (page: number) => {
	if (page === 1) {
		return 'https://meitu001.cc/i/';
	} else {
		return `https://meitu001.cc/i/index_${page}.html`;
	}
};

const ImgListUrlByType = (type: string, page: number) => {
	if (page === 1) {
		return `https://meitu001.cc/tags/${type}/`;
	} else {
		return `https://meitu001.cc/tags/${type}/${page}.html`;
	}
};

const agent = new https.Agent({
	rejectUnauthorized: false,
});

@Injectable()
export class ImglistService {
	async getImgList(page: number) {
		try {
			const response = await axios.get(getImgListUrl(page), {
				httpsAgent: agent,
			});
			const imgList = getAlbumList(response.data as string);

			return new ReqSucc(200, {
				list: imgList,
				hasMore: true,
			});
		} catch (error) {
			console.error(error);
			if (axios.isAxiosError(error) && error.response?.status === 404) {
				return new ReqSucc(200, {
					list: [],
					hasMore: false,
				});
			}
			return new ReqErr(500, '获取图片列表失败');
		}
	}

	async getImgListByType(type: string, page: number) {
		try {
			const url = ImgListUrlByType(type, page);
			const response = await axios.get(url, { httpsAgent: agent });
			const imgList = getAlbumList(response.data as string);

			return new ReqSucc(200, {
				list: imgList,
				hasMore: true,
			});
		} catch (error) {
			console.error(error);
			if (axios.isAxiosError(error) && error.response?.status === 404) {
				return new ReqSucc(200, {
					list: [],
					hasMore: false,
				});
			}
			return new ReqErr(500, '获取图片列表失败');
		}
	}

	getImgTypeList() {
		return new ReqSucc(200, [
			{
				tagName: '网络美女',
				tagId: 'wangluomeinv',
			},
			{
				tagName: '情趣内衣',
				tagId: 'qingquneiyi',
			},
			{
				tagName: '性感诱惑',
				tagId: 'xinggan',
			},
			{
				tagName: '丝袜美腿',
				tagId: 'meitui',
			},
			{
				tagName: '大胸妹子',
				tagId: 'meixiong',
			},
			{
				tagName: '日本制服',
				tagId: 'ribenzhifu',
			},
			{
				tagName: '性感少女',
				tagId: 'xingganshaonv',
			},
			{
				tagName: 'COS女仆',
				tagId: 'cos',
			},
			{
				tagName: '极品嫩模',
				tagId: 'nenmo',
			},
			{
				tagName: '丝足美腿',
				tagId: 'sizu',
			},
			{
				tagName: '台湾正妹',
				tagId: 'taiwanzhengmei',
			},
		]);
	}
}
