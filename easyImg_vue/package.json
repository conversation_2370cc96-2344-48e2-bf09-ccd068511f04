{"name": "easyimg-vue", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "axios": "^1.11.0", "pinia": "^2.1.7"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "sass": "^1.69.5", "typescript": "~5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.25"}}