import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/newList'
  },
  {
    path: '/newList',
    name: 'NewList',
    component: () => import('@/views/NewList.vue'),
    meta: {
      keepAlive: true, // 启用KeepAlive缓存
      title: '最新图集'
    }
  },
  {
    path: '/detail/:id',
    name: 'Detail',
    component: () => import('@/views/Detail.vue'),
    meta: {
      keepAlive: false, // 详情页不缓存
      title: '图片详情'
    }
  },
  {
    path: '/modelAlbum/:tagId',
    name: 'ModelAlbum',
    component: () => import('@/views/ModelAlbum.vue'),
    meta: {
      keepAlive: true, // 启用KeepAlive缓存
      title: '模特专辑'
    }
  },
  {
    path: '/tagAlbum/:tagId',
    name: 'TagAlbum',
    component: () => import('@/views/TagAlbum.vue'),
    meta: {
      keepAlive: true, // 启用KeepAlive缓存
      title: '标签专辑'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - EasyImg`
  }
  next()
})

export default router
