import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { CacheData, CacheState } from '@/types'

export const useCacheStore = defineStore('cache', () => {
  // 状态
  const cacheState = ref<CacheState>({})
  const maxAge = ref(30 * 60 * 1000) // 30分钟
  const maxSize = ref(50) // 最大缓存条目数

  // 计算属性
  const cacheKeys = computed(() => Object.keys(cacheState.value))
  const cacheSize = computed(() => cacheKeys.value.length)

  // 设置缓存
  const setCache = (routeKey: string, data: CacheData) => {
    cacheState.value[routeKey] = {
      data,
      timestamp: Date.now(),
      scrollPosition: cacheState.value[routeKey]?.scrollPosition
    }
    
    // 控制缓存大小
    if (cacheSize.value > maxSize.value) {
      clearOldestCache()
    }
  }

  // 获取缓存
  const getCache = (routeKey: string): CacheData | null => {
    const cache = cacheState.value[routeKey]
    if (!cache) return null
    
    // 检查是否过期
    if (Date.now() - cache.timestamp > maxAge.value) {
      clearCache(routeKey)
      return null
    }
    
    return cache.data
  }

  // 清除缓存
  const clearCache = (routeKey?: string) => {
    if (routeKey) {
      delete cacheState.value[routeKey]
    } else {
      cacheState.value = {}
    }
  }

  // 清除最旧的缓存
  const clearOldestCache = () => {
    const sortedKeys = cacheKeys.value.sort(
      (a, b) => cacheState.value[a].timestamp - cacheState.value[b].timestamp
    )
    const keysToRemove = sortedKeys.slice(0, cacheSize.value - maxSize.value)
    keysToRemove.forEach(key => {
      clearCache(key)
    })
  }

  // 清除过期缓存
  const clearExpiredCache = () => {
    const now = Date.now()
    cacheKeys.value.forEach(key => {
      if (now - cacheState.value[key].timestamp > maxAge.value) {
        clearCache(key)
      }
    })
  }

  // 设置滚动位置
  const setScrollPosition = (routeKey: string, position: { x: number; y: number }) => {
    if (cacheState.value[routeKey]) {
      cacheState.value[routeKey].scrollPosition = position
    } else {
      cacheState.value[routeKey] = {
        data: {},
        timestamp: Date.now(),
        scrollPosition: position
      }
    }
  }

  // 获取滚动位置
  const getScrollPosition = (routeKey: string) => {
    return cacheState.value[routeKey]?.scrollPosition || null
  }

  // 检查是否已缓存
  const isCached = (routeKey: string): boolean => {
    return getCache(routeKey) !== null
  }

  // 定期清理过期缓存
  setInterval(() => {
    clearExpiredCache()
  }, 60000) // 每分钟检查一次

  return {
    cacheState,
    maxAge,
    maxSize,
    cacheKeys,
    cacheSize,
    setCache,
    getCache,
    clearCache,
    setScrollPosition,
    getScrollPosition,
    isCached,
    clearExpiredCache
  }
})
