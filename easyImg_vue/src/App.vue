<template>
  <div id="app">
    <Header />
    <router-view v-slot="{ Component, route }">
      <keep-alive :include="cachedViews">
        <component :is="Component" :key="route.fullPath" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Header from '@/components/Header.vue'

const route = useRoute()

// 计算需要缓存的视图
const cachedViews = computed(() => {
  const views: string[] = []
  
  // 根据路由meta信息决定是否缓存
  if (route.meta?.keepAlive) {
    // 获取组件名称，这里使用路由名称作为组件名称
    if (route.name) {
      views.push(route.name as string)
    }
  }
  
  // 默认缓存的页面
  const defaultCachedViews = ['NewList', 'ModelAlbum', 'TagAlbum']
  
  return [...new Set([...views, ...defaultCachedViews])]
})
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 响应式设计
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

// 图片懒加载样式
img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
  opacity: 1;
}
</style>
