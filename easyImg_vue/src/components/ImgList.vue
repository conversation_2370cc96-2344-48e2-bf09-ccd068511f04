<template>
  <ul class="list-grid">
    <li v-for="item in imgList" :key="item.id" class="list-item">
      <div 
        class="list-item-link"
        @click="handleDetailClick(item.id)"
      >
        <div class="item-image">
          <img :src="item.coverImg" :alt="item.title" />
        </div>
        <div class="item-title">
          <p class="title">{{ item.title }}</p>
          <div v-if="item.modelList && item.modelList.length > 0" class="model-info">
            <span class="model-label">模特:</span>
            <span 
              v-for="(model, index) in item.modelList" 
              :key="model.tagId" 
              class="model-name"
              @click.stop="handleModelClick(model.tagId)"
            >
              {{ model.tagName }}{{ index < item.modelList.length - 1 ? '、' : '' }}
            </span>
          </div>
          <div v-if="item.tagList && item.tagList.length > 0" class="tag-info">
            <span 
              v-for="tag in item.tagList" 
              :key="tag.tagId" 
              class="tag"
              @click.stop="handleTagClick(tag.tagId)"
            >
              {{ tag.tagName }}
            </span>
          </div>
        </div>
      </div>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import type { ImgListType } from '@/types'

interface Props {
  imgList: ImgListType[]
}

defineProps<Props>()

const router = useRouter()

// 处理模特点击事件
const handleModelClick = (modelId: string) => {
  router.push(`/modelAlbum/${modelId}`)
}

// 处理标签点击事件
const handleTagClick = (tagId: string) => {
  router.push(`/tagAlbum/${tagId}`)
}

// 处理详情页面跳转事件
const handleDetailClick = (itemId: number) => {
  router.push(`/detail/${itemId}`)
}
</script>

<style scoped lang="scss">
/* 两列网格布局 */
.list-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  list-style: none;
  padding: 0;
  margin: 0;
}

/* 列表项样式 */
.list-item {
  height: 80vw;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* Link组件样式 */
.list-item-link {
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  height: 100%;
  cursor: pointer;
}

/* 图片容器 */
.item-image {
  flex: 1;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

/* 标题容器 */
.item-title {
  padding: 8px;
  background: white;
  flex-shrink: 0;

  .title {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
  }

  .model-info {
    margin-bottom: 6px;
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;

    .model-label {
      font-weight: 500;
      color: #333;
    }

    .model-name {
      color: #007bff;
      font-weight: 400;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .tag-info {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .tag {
      background: #f0f0f0;
      color: #666;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 400;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: #e0e0e0;
      }
    }
  }
}
</style>
