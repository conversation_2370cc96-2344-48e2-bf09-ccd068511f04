<template>
  <header class="header">
    <div class="container">
      <div class="hot-tags">
        <span>热门标签</span>
        <div class="tag-list">
          <span class="tag" @click="handleLatestAlbumClick">
            最新图集
          </span>
          <span
            v-for="tag in hotTags"
            :key="tag.tagId"
            class="tag"
            @click="handleTagClick(tag)"
          >
            {{ tag.tagName }}
          </span>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getHotTags } from '@/api/imgList'
import type { TagItem } from '@/types'

const router = useRouter()
const hotTags = ref<TagItem[]>([])

// 获取热门标签数据
const fetchHotTags = async () => {
  try {
    const tags = await getHotTags()
    hotTags.value = tags
  } catch (error) {
    console.error('获取热门标签失败:', error)
  }
}

const handleTagClick = (tag: TagItem) => {
  // 跳转到标签页面
  router.push(`/tagAlbum/${tag.tagId}`)
}

const handleLatestAlbumClick = () => {
  // 跳转到最新图集页面
  router.push('/newList')
}

onMounted(() => {
  fetchHotTags()
})
</script>

<style scoped lang="scss">
.header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .hot-tags {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    > span {
      font-weight: 500;
      color: #333;
      font-size: 14px;
      white-space: nowrap;
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag {
        background: #f5f5f5;
        color: #666;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;

        &:hover {
          background: #007bff;
          color: white;
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .header {
    .hot-tags {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .tag-list {
        width: 100%;
      }
    }
  }
}
</style>
