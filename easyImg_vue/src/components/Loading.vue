<template>
  <div>
    <div v-if="loading" class="loading">
      <p>加载中...</p>
    </div>
    <div v-if="!hasMore && currentLength > 0" class="no-more">
      <p>没有更多数据了</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  loading: boolean
  hasMore: boolean
  currentLength: number
}

defineProps<Props>()
</script>

<style scoped lang="scss">
.loading {
  text-align: center;
  padding: 20px;
  color: #666;

  p {
    margin: 0;
    font-size: 14px;
  }
}

.no-more {
  text-align: center;
  padding: 20px;
  color: #999;

  p {
    margin: 0;
    font-size: 14px;
  }
}
</style>
