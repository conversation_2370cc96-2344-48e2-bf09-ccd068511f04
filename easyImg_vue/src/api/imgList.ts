import request from '@/utils/request'
import type { AlbumType, TagItem } from '@/types'

// 获取图片列表
export const getImgList = async (page: number): Promise<AlbumType> => {
  const res = await request.get<AlbumType>('/api/imgList/all', {
    params: {
      page
    }
  })
  return res
}

// 获取热门标签
export const getHotTags = async (): Promise<TagItem[]> => {
  const res = await request.get<TagItem[]>('/api/imgList/tagList')
  return res
}

// 根据标签ID获取图片列表
export const getImgListByTag = async (tagId: string, page: number): Promise<AlbumType> => {
  const res = await request.get<AlbumType>('/api/imgList/getImgListByType', {
    params: {
      tagId,
      page
    }
  })
  return res
}

// 根据模特ID获取图片列表
export const getImgListByModel = async (modelId: string, page: number): Promise<AlbumType> => {
  const res = await request.get<AlbumType>('/api/imgList/getImgListByType', {
    params: {
      modelId,
      page
    }
  })
  return res
}
