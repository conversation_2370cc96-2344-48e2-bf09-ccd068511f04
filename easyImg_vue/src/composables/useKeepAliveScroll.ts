import { ref, onMounted, onActivated, onBeforeUnmount } from 'vue'

/**
 * 专门为KeepAlive组件设计的滚动位置管理
 * 由于KeepAlive会保持组件实例，我们只需要简单的滚动位置保存和恢复
 */
export function useKeepAliveScroll() {
  const scrollPosition = ref(0)

  // 保存滚动位置
  const saveScrollPosition = () => {
    scrollPosition.value = window.scrollY
  }

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    if (scrollPosition.value > 0) {
      setTimeout(() => {
        window.scrollTo(0, scrollPosition.value)
      }, 10)
    }
  }

  // 重置滚动位置
  const resetScrollPosition = () => {
    scrollPosition.value = 0
  }

  // 滚动事件处理
  let scrollTimer: number | null = null
  const handleScroll = () => {
    if (scrollTimer) clearTimeout(scrollTimer)
    scrollTimer = setTimeout(() => {
      saveScrollPosition()
    }, 100)
  }

  // 组件被KeepAlive激活时恢复滚动位置
  onActivated(() => {
    restoreScrollPosition()
  })

  // 添加滚动监听
  onMounted(() => {
    window.addEventListener('scroll', handleScroll, { passive: true })
  })

  // 清理事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScroll)
    if (scrollTimer) clearTimeout(scrollTimer)
  })

  return {
    scrollPosition,
    saveScrollPosition,
    restoreScrollPosition,
    resetScrollPosition
  }
}
