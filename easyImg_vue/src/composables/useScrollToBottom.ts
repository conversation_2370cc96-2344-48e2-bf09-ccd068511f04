import { onMounted, onUnmounted, ref, watch } from 'vue'
import type { UseScrollToBottomOptions } from '@/types'

export function useScrollToBottom(options: UseScrollToBottomOptions = {}) {
  const {
    threshold = 100,
    enabled = true,
    onScrollToBottom
  } = options

  const isScrolling = ref(false)

  const handleScroll = () => {
    if (!enabled || !onScrollToBottom || isScrolling.value) return

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.scrollHeight

    // 当滚动到距离底部指定阈值时触发回调
    if (scrollTop + windowHeight >= documentHeight - threshold) {
      isScrolling.value = true
      onScrollToBottom()

      // 防止短时间内重复触发
      setTimeout(() => {
        isScrolling.value = false
      }, 500)
    }
  }

  const checkScroll = () => {
    handleScroll()
  }

  // 监听enabled变化，动态添加/移除事件监听器
  watch(() => enabled, (newEnabled: boolean) => {
    if (newEnabled) {
      window.addEventListener('scroll', handleScroll, { passive: true })
    } else {
      window.removeEventListener('scroll', handleScroll)
    }
  })

  onMounted(() => {
    if (enabled) {
      window.addEventListener('scroll', handleScroll, { passive: true })
    }
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
  })

  return {
    checkScroll
  }
}
