import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useCacheStore } from '@/stores/cache'
import type { UsePageCacheOptions } from '@/types'

// 页面缓存Hook
export function usePageCache<T>(
  initialState: T,
  options: UsePageCacheOptions = {}
) {
  const {
    cacheKey,
    dependencies = [],
    autoRestore = true,
    autoSave = true,
    saveInterval = 5000 // 5秒
  } = options

  const route = useRoute()
  const cacheStore = useCacheStore()
  const finalCacheKey = cacheKey || (route.path + route.fullPath)

  // 初始化状态
  const state = ref<T>(initialState)

  // 保存状态到缓存
  const saveToCache = (currentState: T) => {
    cacheStore.setCache(finalCacheKey, {
      state: currentState,
      dependencies,
      timestamp: Date.now()
    })
  }

  // 从缓存恢复状态
  const restoreFromCache = (): boolean => {
    const cached = cacheStore.getCache(finalCacheKey)
    if (cached?.state) {
      state.value = cached.state
      return true
    }
    return false
  }

  // 清除缓存
  const clearPageCache = () => {
    cacheStore.clearCache(finalCacheKey)
  }

  // 初始化时尝试从缓存恢复状态
  onMounted(() => {
    if (autoRestore) {
      const cached = cacheStore.getCache(finalCacheKey)
      if (cached?.state) {
        // 检查依赖项是否变化
        if (dependencies.length > 0 && cached.dependencies) {
          const depsChanged = dependencies.length !== cached.dependencies.length ||
            dependencies.some((dep, index) => dep !== cached.dependencies[index])
          
          if (depsChanged) {
            // 依赖项变化，清除缓存并重置状态
            clearPageCache()
            state.value = initialState
          } else {
            state.value = cached.state
          }
        } else {
          state.value = cached.state
        }
      }
    }
  })

  // 监听状态变化，自动保存到缓存
  if (autoSave) {
    watch(
      state,
      (newState) => {
        saveToCache(newState)
      },
      { deep: true }
    )
  }

  // 自动保存定时器
  let saveTimer: number | null = null
  if (autoSave && saveInterval > 0) {
    onMounted(() => {
      saveTimer = setInterval(() => {
        saveToCache(state.value)
      }, saveInterval)
    })

    onUnmounted(() => {
      if (saveTimer) {
        clearInterval(saveTimer)
      }
    })
  }

  // 页面卸载时保存状态
  onUnmounted(() => {
    if (autoSave) {
      saveToCache(state.value)
    }
  })

  return {
    state,
    saveToCache,
    restoreFromCache,
    clearPageCache
  }
}

// 手动缓存Hook
export function useManualCache<T>(cacheKey?: string) {
  const route = useRoute()
  const cacheStore = useCacheStore()
  const finalCacheKey = cacheKey || (route.path + route.fullPath)

  // 手动设置缓存
  const setCacheData = (data: T) => {
    cacheStore.setCache(finalCacheKey, {
      state: data,
      timestamp: Date.now()
    })
  }

  // 手动获取缓存
  const getCacheData = (): T | null => {
    const cached = cacheStore.getCache(finalCacheKey)
    return cached?.state || null
  }

  // 手动清除缓存
  const clearCacheData = () => {
    cacheStore.clearCache(finalCacheKey)
  }

  return {
    setCacheData,
    getCacheData,
    clearCacheData,
    cacheKey: finalCacheKey
  }
}

// 滚动位置缓存Hook
export function useScrollCache(options: UsePageCacheOptions = {}) {
  const { cacheKey, autoRestore = true, autoSave = true } = options
  const route = useRoute()
  const cacheStore = useCacheStore()
  const finalCacheKey = cacheKey || (route.path + route.fullPath)

  // 保存滚动位置
  const saveScrollPosition = () => {
    const position = {
      x: window.scrollX,
      y: window.scrollY
    }
    cacheStore.setScrollPosition(finalCacheKey, position)
  }

  // 恢复滚动位置
  const restoreScrollPosition = (): boolean => {
    const position = cacheStore.getScrollPosition(finalCacheKey)
    if (position) {
      window.scrollTo(position.x, position.y)
      return true
    }
    return false
  }

  // 自动保存滚动位置
  if (autoSave) {
    onMounted(() => {
      const handleScroll = () => {
        saveScrollPosition()
      }
      window.addEventListener('scroll', handleScroll, { passive: true })
      
      onUnmounted(() => {
        window.removeEventListener('scroll', handleScroll)
      })
    })
  }

  // 自动恢复滚动位置
  if (autoRestore) {
    onMounted(() => {
      nextTick(() => {
        // 延迟恢复，确保页面渲染完成
        setTimeout(() => {
          restoreScrollPosition()
        }, 10)
      })
    })
  }

  return {
    saveScrollPosition,
    restoreScrollPosition
  }
}
