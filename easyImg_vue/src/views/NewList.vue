<template>
  <div class="list-page">
    <ImgList :img-list="list" />
    <Loading 
      :loading="loading" 
      :has-more="hasMore" 
      :current-length="list.length" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getImgList } from '@/api/imgList'
import { useScrollToBottom } from '@/composables/useScrollToBottom'
import { useManualCache, useScrollCache } from '@/composables/usePageCache'
import ImgList from '@/components/ImgList.vue'
import Loading from '@/components/Loading.vue'
import type { ImgListType } from '@/types'

// 使用手动缓存Hook
const { setCacheData, getCacheData } = useManualCache<{
  list: ImgListType[]
  hasMore: boolean
  page: number
}>()

// 本地状态
const list = ref<ImgListType[]>([])
const hasMore = ref(true)
const page = ref(1)
const loading = ref(false)

// 手动加载数据
const loadData = async (pageNum: number, append: boolean = false) => {
  if (loading.value) return
  
  loading.value = true
  try {
    const data = await getImgList(pageNum)
    
    const newList = append ? [...list.value, ...data.list] : data.list
    const newPage = pageNum + 1
    
    // 更新本地状态
    list.value = newList
    hasMore.value = data.hasMore
    page.value = newPage
    
    // 更新缓存
    setCacheData({
      list: newList,
      hasMore: data.hasMore,
      page: newPage
    })
    
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载更多数据
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadData(page.value, true)
  }
}

// 初始加载数据
onMounted(() => {
  // 尝试从缓存恢复数据
  const cachedData = getCacheData()
  if (cachedData && cachedData.list.length > 0) {
    list.value = cachedData.list
    hasMore.value = cachedData.hasMore
    page.value = cachedData.page
  } else {
    // 缓存中没有数据，加载第一页
    loadData(1, false)
  }
})

// 使用滚动位置缓存
useScrollCache({
  autoRestore: true,
  autoSave: true
})

// 使用自定义Hook检测滚动到底部
useScrollToBottom({
  threshold: 100,
  enabled: computed(() => hasMore.value && !loading.value).value,
  onScrollToBottom: loadMore
})
</script>

<style scoped lang="scss">
/* 列表页面样式 - 移动端适配 */
.list-page {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
}
</style>
