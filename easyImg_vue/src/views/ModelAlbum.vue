<template>
  <div class="album-page">
    <div class="album-header">
      <span class="back-link" @click="goBack">←</span>
      <h1>模特专辑</h1>
    </div>
    <ImgList :img-list="list" />
    <Loading 
      :loading="loading" 
      :has-more="hasMore" 
      :current-length="list.length" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getImgListByModel } from '@/api/imgList'
import { useScrollToBottom } from '@/composables/useScrollToBottom'
import { useKeepAliveScroll } from '@/composables/useKeepAliveScroll'
import ImgList from '@/components/ImgList.vue'
import Loading from '@/components/Loading.vue'
import type { ImgListType } from '@/types'

const route = useRoute()
const router = useRouter()

// 本地状态 - KeepAlive会自动保持这些状态
const list = ref<ImgListType[]>([])
const hasMore = ref(true)
const page = ref(1)
const loading = ref(false)
const currentModelId = ref('')

// 使用KeepAlive滚动管理
const { resetScrollPosition } = useKeepAliveScroll()

// 手动加载数据
const loadData = async (pageNum: number, append: boolean = false) => {
  if (loading.value) return

  const modelId = route.params.tagId as string
  loading.value = true

  try {
    const data = await getImgListByModel(modelId, pageNum)

    const newList = append ? [...list.value, ...data.list] : data.list
    const newPage = pageNum + 1

    // 更新本地状态 - KeepAlive会自动保持
    list.value = newList
    hasMore.value = data.hasMore
    page.value = newPage

  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载更多数据
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadData(page.value, true)
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 监听路由参数变化，如果modelId变化则重新加载
watch(() => route.params.tagId, (newModelId) => {
  const modelId = newModelId as string
  if (modelId !== currentModelId.value) {
    // 路由参数变化，重置状态并重新加载
    currentModelId.value = modelId
    list.value = []
    hasMore.value = true
    page.value = 1
    resetScrollPosition() // 重置滚动位置
    loadData(1, false)
  }
}, { immediate: true })

// 初始加载数据
onMounted(() => {
  const modelId = route.params.tagId as string
  currentModelId.value = modelId

  // 如果没有数据，说明是首次加载
  if (list.value.length === 0) {
    loadData(1, false)
  }
})

// 使用自定义Hook检测滚动到底部
useScrollToBottom({
  threshold: 100,
  enabled: computed(() => hasMore.value && !loading.value).value,
  onScrollToBottom: loadMore
})
</script>

<style scoped lang="scss">
.album-page {
  background: #f5f5f5;
  min-height: 100vh;
}

.album-header {
  background: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;

  .back-link {
    font-size: 20px;
    cursor: pointer;
    color: #007bff;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f0f0f0;
    }

    &:active {
      background: #e0e0e0;
    }
  }

  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

.album-page :deep(.list-grid) {
  padding: 16px;
}
</style>
