<template>
  <div class="detail-page">
    <div class="detail-header">
      <span class="back-link" @click="goBack">←</span>
      <h1>{{ imgDetail.title }}</h1>
    </div>
    <div class="detail-content">
      <img 
        v-for="(item, index) in visibleImages"
        :key="index"
        :src="item" 
        :alt="`${imgDetail.title}-${index}`"
        loading="lazy"
      />
    </div>
    <div 
      v-if="visibleImages.length >= imgDetail.imgUrlList.length && imgDetail.imgUrlList.length > 0" 
      class="no-more"
    >
      <p>已显示全部图片</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getImgDetail } from '@/api/imgDetail'
import { useScrollToBottom } from '@/composables/useScrollToBottom'
import type { ImgDetail } from '@/types'

const route = useRoute()
const router = useRouter()

const imgDetail = ref<ImgDetail>({
  imgUrlList: [],
  total: 0,
  title: ''
})
const visibleImageCount = ref(1990) // 初始显示的图片数量

// 计算可见的图片列表
const visibleImages = computed(() => {
  return imgDetail.value.imgUrlList.slice(0, visibleImageCount.value)
})

// 加载更多图片（懒加载）
const loadMoreImages = () => {
  if (visibleImageCount.value >= imgDetail.value.imgUrlList.length) return
  visibleImageCount.value = Math.min(
    visibleImageCount.value + 10, 
    imgDetail.value.imgUrlList.length
  )
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 使用自定义Hook检测滚动到底部
useScrollToBottom({
  threshold: 200,
  enabled: visibleImageCount.value < imgDetail.value.imgUrlList.length,
  onScrollToBottom: loadMoreImages
})

onMounted(async () => {
  const id = route.params.id as string
  try {
    const res = await getImgDetail(Number(id))
    imgDetail.value = res
    // 重置可见图片数量
    visibleImageCount.value = Math.min(100000, res.imgUrlList.length)
  } catch (error) {
    console.error('获取图片详情失败:', error)
  }
})
</script>

<style scoped lang="scss">
.detail-page {
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-header {
  background: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;

  .back-link {
    font-size: 20px;
    cursor: pointer;
    color: #007bff;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f0f0f0;
    }

    &:active {
      background: #e0e0e0;
    }
  }

  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.detail-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.02);
    }
  }
}

.no-more {
  text-align: center;
  padding: 20px;
  color: #999;

  p {
    margin: 0;
    font-size: 14px;
  }
}
</style>
