// 标签项类型
export interface TagItem {
  tagName: string
  tagId: string
}

// 图片列表项类型
export interface ImgListType {
  coverImg: string
  title: string
  id: number
  modelList: TagItem[]
  tagList: TagItem[]
}

// 专辑类型
export interface AlbumType {
  list: ImgListType[]
  hasMore: boolean
}

// 图片详情类型
export interface ImgDetail {
  imgUrlList: string[]
  total: number
  title: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message?: string
}

// 缓存数据类型
export interface CacheData {
  [key: string]: any
}

// 缓存状态接口
export interface CacheState {
  [routeKey: string]: {
    data: CacheData
    timestamp: number
    scrollPosition?: { x: number; y: number }
  }
}

// 滚动到底部选项
export interface UseScrollToBottomOptions {
  threshold?: number
  enabled?: boolean
  onScrollToBottom?: () => void
}

// 页面缓存选项
export interface UsePageCacheOptions {
  cacheKey?: string
  dependencies?: any[]
  autoRestore?: boolean
  autoSave?: boolean
  saveInterval?: number
}
