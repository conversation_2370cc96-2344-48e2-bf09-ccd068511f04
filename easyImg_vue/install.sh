#!/bin/bash

echo "🚀 开始安装 EasyImg Vue3 项目依赖..."

# 检查是否安装了 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 请先安装 Node.js (版本 >= 16)"
    exit 1
fi

# 检查 Node.js 版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ 错误: Node.js 版本过低，请升级到 16 或更高版本"
    exit 1
fi

echo "✅ Node.js 版本检查通过: $(node -v)"

# 检查包管理器
if command -v pnpm &> /dev/null; then
    PACKAGE_MANAGER="pnpm"
    echo "📦 使用 pnpm 安装依赖..."
elif command -v yarn &> /dev/null; then
    PACKAGE_MANAGER="yarn"
    echo "📦 使用 yarn 安装依赖..."
else
    PACKAGE_MANAGER="npm"
    echo "📦 使用 npm 安装依赖..."
fi

# 安装依赖
$PACKAGE_MANAGER install

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功!"
    echo ""
    echo "🎉 项目安装完成! 可以使用以下命令:"
    echo "   开发模式: $PACKAGE_MANAGER run dev"
    echo "   构建项目: $PACKAGE_MANAGER run build"
    echo "   预览构建: $PACKAGE_MANAGER run preview"
    echo ""
    echo "📖 更多信息请查看 README.md"
else
    echo "❌ 依赖安装失败，请检查网络连接或尝试手动安装"
    exit 1
fi
