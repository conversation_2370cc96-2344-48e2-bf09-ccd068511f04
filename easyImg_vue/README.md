# EasyImg Vue3 版本

这是 EasyImg 项目的 Vue3 重写版本，使用了 Vue3 的 Composition API 和 KeepAlive 组件实现了原 React 版本的所有功能。

## 主要特性

### 🚀 技术栈
- **Vue 3** - 使用 Composition API
- **TypeScript** - 完整的类型支持
- **Vue Router 4** - 路由管理
- **Pinia** - 状态管理
- **Vite** - 构建工具
- **SCSS** - 样式预处理器
- **Axios** - HTTP 请求库

### 📦 缓存系统
使用 Vue3 的 **KeepAlive** 组件完全替代了原 React 版本的复杂缓存系统：

1. **组件实例缓存** - KeepAlive 自动缓存整个组件实例，包括所有状态
2. **响应式数据保持** - ref、reactive 等响应式数据自动保持
3. **滚动位置缓存** - 通过简单的 useKeepAliveScroll 组合式函数实现
4. **零配置缓存** - 无需手动管理缓存生命周期，Vue3 自动处理

### 🎯 核心功能

#### 页面组件
- **NewList** - 最新图集列表页（支持缓存）
- **Detail** - 图片详情页（不缓存，每次重新加载）
- **ModelAlbum** - 模特专辑页（支持缓存）
- **TagAlbum** - 标签专辑页（支持缓存）

#### 组合式 API 函数
- **useScrollToBottom** - 滚动到底部检测
- **useKeepAliveScroll** - KeepAlive 专用滚动位置管理

#### 组件
- **Header** - 顶部导航和热门标签
- **ImgList** - 图片列表展示
- **Loading** - 加载状态组件

### 🔧 缓存配置

在路由配置中通过 `meta.keepAlive` 控制页面是否缓存：

```typescript
{
  path: '/newList',
  name: 'NewList',
  component: () => import('@/views/NewList.vue'),
  meta: {
    keepAlive: true, // 启用KeepAlive缓存
    title: '最新图集'
  }
}
```

### 📱 响应式设计
- 移动端优先设计
- 两列网格布局
- 触摸友好的交互
- 自适应图片加载

### ⚡ 性能优化
- 图片懒加载
- 虚拟滚动（通过分页实现）
- 组件按需加载
- 智能缓存策略

## 安装和运行

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 项目结构

```
easyImg_vue/
├── src/
│   ├── api/                 # API 接口
│   ├── components/          # 公共组件
│   ├── composables/         # 组合式 API 函数
│   ├── router/              # 路由配置
│   ├── stores/              # Pinia 状态管理
│   ├── styles/              # 全局样式
│   ├── types/               # TypeScript 类型定义
│   ├── utils/               # 工具函数
│   ├── views/               # 页面组件
│   ├── App.vue              # 根组件
│   └── main.ts              # 入口文件
├── public/                  # 静态资源
├── index.html               # HTML 模板
├── vite.config.ts           # Vite 配置
├── tsconfig.json            # TypeScript 配置
└── package.json             # 项目配置
```

## 与 React 版本的主要差异

1. **缓存实现**：使用 Vue3 的 KeepAlive 替代自定义缓存系统
2. **状态管理**：使用 Pinia 替代 React Context
3. **组合式 API**：使用 Vue3 的 Composition API 替代 React Hooks
4. **路由管理**：使用 Vue Router 4 替代 React Router
5. **构建工具**：使用 Vite 替代 Create React App

## 缓存策略说明

### KeepAlive 缓存
- 自动缓存组件实例和状态
- 支持动态包含/排除组件
- 内存管理更加高效

### 数据缓存
- 使用 Pinia 存储列表数据
- 支持缓存过期和大小限制
- 自动清理过期缓存

### 滚动位置缓存
- 自动保存页面滚动位置
- 页面返回时自动恢复
- 支持多级路由缓存
