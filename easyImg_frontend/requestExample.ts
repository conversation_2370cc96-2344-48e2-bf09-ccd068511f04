import request from './request';

// 使用示例

// 1. GET请求示例
export const getUserInfo = async (userId: string) => {
  try {
    const data = await request.get(`/user/${userId}`);
    return data;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
};

// 2. POST请求示例
export const createUser = async (userData: any) => {
  try {
    const data = await request.post('/user', userData);
    return data;
  } catch (error) {
    console.error('创建用户失败:', error);
    throw error;
  }
};

// 3. PUT请求示例
export const updateUser = async (userId: string, userData: any) => {
  try {
    const data = await request.put(`/user/${userId}`, userData);
    return data;
  } catch (error) {
    console.error('更新用户失败:', error);
    throw error;
  }
};

// 4. DELETE请求示例
export const deleteUser = async (userId: string) => {
  try {
    const data = await request.delete(`/user/${userId}`);
    return data;
  } catch (error) {
    console.error('删除用户失败:', error);
    throw error;
  }
};

// 5. 带查询参数的GET请求示例
export const getUsers = async (params: { page: number; limit: number }) => {
  try {
    const data = await request.get('/users', { params });
    return data;
  } catch (error) {
    console.error('获取用户列表失败:', error);
    throw error;
  }
};

// 6. 带自定义配置的请求示例
export const uploadFile = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const data = await request.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return data;
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
};
