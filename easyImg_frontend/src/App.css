/* 移动端适配的全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f5f5;
}

.App {
  max-width: 100%;
  margin: 0 auto;
  min-height: 100vh;
  background: #f5f5f5;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 关键属性：启用惯性滚动 */
}

/* 移动端触摸优化 */
button, a {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 防止页面缩放 */
html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
