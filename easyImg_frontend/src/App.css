/* 移动端适配的全局样式 */
* {
  box-sizing: border-box;
}

:global {
  body {
    -webkit-overflow-scrolling: touch;
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f5f5;
}

.App {
  max-width: 100%;
  margin: 0 auto;
  min-height: 100vh;
  background: #f5f5f5;
}

/* 移动端触摸优化 */
button, a {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 防止页面缩放 */
html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
