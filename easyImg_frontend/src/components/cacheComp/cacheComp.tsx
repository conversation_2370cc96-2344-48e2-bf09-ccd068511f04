import React, { createContext, useContext, ReactNode, useState } from 'react';

// 定义 cache 的类型
interface Cache {
  [key: string]: {
    data: any;
    timestamp: number;
  };
}

// 定义 context 的类型
interface CacheContextType {
  cache: Cache;
  setCache: (key: string, data: any) => void;
  getCache: (key: string) => any;
}

// 创建 context
export const CacheContext = createContext<CacheContextType>({
  cache: {},
  setCache: () => { },
  getCache: () => { },
});

// Provider 组件的 props 类型
interface CacheProviderProps {
  children: ReactNode;
  maxCacheSize?: number;
}

// Provider 组件
const CacheProvider: React.FC<CacheProviderProps> = ({ children, maxCacheSize = 10 }) => {
  const [cache, setCacheState] = useState<Cache>({});

  const setCache = (key: string, data: any) => {
    const newCache = {
      ...cache,
      [key]: { data, timestamp: Date.now() }
    };

    // 检查缓存数量是否超过限制
    const cacheKeys = Object.keys(newCache);
    if (cacheKeys.length > maxCacheSize) {
      // 找到最早的缓存项
      let oldestKey = cacheKeys[0];
      let oldestTimestamp = newCache[oldestKey].timestamp;

      for (const key of cacheKeys) {
        if (newCache[key].timestamp < oldestTimestamp) {
          oldestKey = key;
          oldestTimestamp = newCache[key].timestamp;
        }
      }

      // 删除最早的缓存项
      delete newCache[oldestKey];
    }

    setCacheState(newCache);
  };

  const getCache = (key: string) => {
    return cache[key]?.data;
  };

  return (
    <CacheContext.Provider value={{ cache, setCache, getCache }}>
      {children}
    </CacheContext.Provider>
  );
};

export { CacheProvider };
