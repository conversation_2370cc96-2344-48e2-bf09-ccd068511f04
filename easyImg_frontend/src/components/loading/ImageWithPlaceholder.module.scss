/* 图片容器样式 */
.imageContainer {
    position: relative;
    width: 100%;
    min-height: 200px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
}

/* 占满模式容器样式 */
.coverMode {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: auto;
}

/* 占位图样式 */
.placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    z-index: 1;
}

.placeholderContent {
    text-align: center;
    color: #6c757d;
}

.placeholderContent p {
    margin: 12px 0 0 0;
    font-size: 14px;
    color: #6c757d;
}

/* 加载动画 */
.loadingSpinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 实际图片样式 */
.actualImage {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: opacity 0.3s ease-in-out;

    &.loaded {
        opacity: 1;
    }
}

/* 占满模式图片样式 */
.coverImage {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover;
    border-radius: 8px;
}

/* 错误状态样式 */
.errorPlaceholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    z-index: 1;
}

.errorContent {
    text-align: center;
    color: #dc3545;
}

.errorIcon {
    font-size: 24px;
    margin-bottom: 8px;
}

.errorContent p {
    margin: 0;
    font-size: 14px;
    color: #dc3545;
}