import React, { useState } from 'react';
import styles from './ImageWithPlaceholder.module.scss';

interface ImageWithPlaceholderProps {
    src: string;
    alt: string;
    className?: string;
    placeholderText?: string;
    cover?: boolean; // 是否占满组件并裁剪
    onLoad?: () => void;
    onError?: () => void;
}

const ImageWithPlaceholder: React.FC<ImageWithPlaceholderProps> = ({
    src,
    alt,
    className = '',
    placeholderText = '加载中...',
    cover = false,
    onLoad,
    onError,
}) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [hasError, setHasError] = useState(false);

    const handleImageLoad = () => {
        setIsLoaded(true);
        onLoad?.();
    };

    const handleImageError = () => {
        setIsLoaded(true);
        setHasError(true);
        onError?.();
    };

    return (
        <div className={`${styles.imageContainer} ${className} ${cover ? styles.coverMode : ''}`}>
            {/* 占位图 */}
            {!isLoaded && (
                <div className={styles.placeholder}>
                    <div className={styles.placeholderContent}>
                        <div className={styles.loadingSpinner}></div>
                        <p>{placeholderText}</p>
                    </div>
                </div>
            )}

            {/* 实际图片 */}
            <img
                src={src}
                alt={alt}
                className={`${styles.actualImage} ${isLoaded ? styles.loaded : ''} ${cover ? styles.coverImage : ''}`}
                onLoad={handleImageLoad}
                onError={handleImageError}
                style={{ display: isLoaded ? 'block' : 'none' }}
            />

            {/* 错误状态 */}
            {hasError && (
                <div className={styles.errorPlaceholder}>
                    <div className={styles.errorContent}>
                        <div className={styles.errorIcon}>⚠️</div>
                        <p>图片加载失败</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ImageWithPlaceholder;
