import React from 'react';
import styles from './loading.module.scss';
import { ImgListType } from '@/remote/imgList/all';

const Loading: React.FC<{
  loading: boolean;
  hasMore: boolean;
  currentLength: number;
}> = ({ loading, hasMore, currentLength }) => {
  return <div>
  {loading && (
        <div className={styles.loading}>
          <p>加载中...</p>
        </div>
      )}
      {!hasMore && currentLength > 0 && (
        <div className={styles.noMore}>
          <p>没有更多数据了</p>
        </div>
      )}  
  </div>
};

export default Loading;