
/* 两列网格布局 */
.listGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  /* 列表项样式 */
  .listItem {
    height: 80vw;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
  
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  
    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  /* Link组件样式 */
  .listItemLink {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  /* 图片容器 */
  .itemImage {
    flex: 1;
    overflow: hidden;
  
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
  
    &:hover img {
      transform: scale(1.05);
    }
  }
  
  /* 标题容器 */
  .itemTitle {
    padding: 8px;
    background: white;
    flex-shrink: 0;
  
    .title {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
    }
  
    .modelInfo {
      margin-bottom: 6px;
      font-size: 12px;
      color: #666;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 4px;
  
      .modelLabel {
        font-weight: 500;
        color: #333;
      }
  
      .modelName {
        color: #007bff;
        font-weight: 400;
      }
    }
  
    .tagInfo {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
  
      .tag {
        background: #f0f0f0;
        color: #666;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 400;
      }
    }
  }