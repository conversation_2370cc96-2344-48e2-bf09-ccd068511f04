import React from 'react';
import styles from './imgList.module.scss';
import type { ImgListType } from '@/remote/imgList/all';
import { useNavigate } from 'react-router-dom';

const ImgList: React.FC<{
  imgList: ImgListType[];
}> = ({ imgList }) => {
  const navigate = useNavigate();

  // 处理模特点击事件
  const handleModelClick = (e: React.MouseEvent, modelId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/modelAlbum/${modelId}`);
  };

  // 处理标签点击事件
  const handleTagClick = (e: React.MouseEvent, tagId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/tagAlbum/${tagId}`);
  };

  // 处理详情页面跳转事件
  const handleDetailClick = (e: React.MouseEvent, itemId: number | string) => {
    e.preventDefault();
    navigate(`/detail/${itemId}`);
  };

  return <ul className={styles.listGrid}>
    {imgList.map((item) => (
          <li key={item.id} className={styles.listItem}>
            <div 
              className={styles.listItemLink}
              onClick={(e) => handleDetailClick(e, item.id)}
              style={{ cursor: 'pointer' }}
            >
              <div className={styles.itemImage}>
                <img src={item.coverImg} alt={item.title} />
              </div>
              <div className={styles.itemTitle}>
                <p className={styles.title}>{item.title}</p>
                {item.modelList && item.modelList.length > 0 && (
                  <div className={styles.modelInfo}>
                    <span className={styles.modelLabel}>模特:</span>
                    {item.modelList.map((model, index) => (
                      <span 
                        key={model.tagId} 
                        className={styles.modelName}
                        onClick={(e) => handleModelClick(e, model.tagId)}
                        style={{ cursor: 'pointer' }}
                      >
                        {model.tagName}
                        {index < item.modelList.length - 1 ? '、' : ''}
                      </span>
                    ))}
                  </div>
                )}
                {item.tagList && item.tagList.length > 0 && (
                  <div className={styles.tagInfo}>
                    {item.tagList.map((tag) => (
                      <span 
                        key={tag.tagId} 
                        className={styles.tag}
                        onClick={(e) => handleTagClick(e, tag.tagId)}
                        style={{ cursor: 'pointer' }}
                      >
                        {tag.tagName}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </li>
        ))}
        </ul>
};

export default ImgList;