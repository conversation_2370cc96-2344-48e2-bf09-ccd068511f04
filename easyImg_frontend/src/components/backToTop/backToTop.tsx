import React, { useState, useEffect } from 'react';
import styles from './backToTop.module.scss';

const BackToTop: React.FC = () => {
    const [isVisible, setIsVisible] = useState(false);
    const [lastScrollY, setLastScrollY] = useState(0);

    // 监听滚动事件
    useEffect(() => {
        const toggleVisibility = () => {
            const currentScrollY = window.pageYOffset;

            // 只有当页面向上滑动且滚动距离超过300px时才显示
            if (currentScrollY > 300 && currentScrollY < lastScrollY) {
                setIsVisible(true);
            } else {
                setIsVisible(false);
            }

            setLastScrollY(currentScrollY);
        };

        window.addEventListener('scroll', toggleVisibility);

        return () => {
            window.removeEventListener('scroll', toggleVisibility);
        };
    }, [lastScrollY]);

    // 回到顶部函数
    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    return (
        <>
            {isVisible && (
                <button
                    className={styles.backToTop}
                    onClick={scrollToTop}
                    aria-label="回到顶部"
                    title="回到顶部"
                >
                    <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M12 4L4 12H9V20H15V12H20L12 4Z"
                            fill="currentColor"
                        />
                    </svg>
                </button>
            )}
        </>
    );
};

export default BackToTop;
