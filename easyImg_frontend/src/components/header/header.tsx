import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './header.module.scss';
import { getHotTags } from '@/remote/imgList/tagList';
import type { TagItem } from '@/remote/imgList/all';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const [hotTags, setHotTags] = useState<TagItem[]>([]);

  // 获取热门标签数据
  useEffect(() => {
    const fetchHotTags = async () => {
      try {
        const tags = await getHotTags();
        setHotTags(tags);
      } catch (error) {
        console.error('获取热门标签失败:', error);
      } finally {
      }
    };

    fetchHotTags();
  }, []);

  const handleTagClick = (tag: TagItem) => {
    // 跳转到标签页面
    navigate(`/tagAlbum/${tag.tagId}`);
  };

  const handleLatestAlbumClick = () => {
    // 跳转到最新图集页面
    navigate('/newList');
  };

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <div className={styles.hotTags}>
          <span>热门标签</span>
            <div className={styles.tagList}>
                <span className={styles.tag} onClick={handleLatestAlbumClick}>
                最新图集
                </span>
              {hotTags.map((tag) => (
                <span
                  key={tag.tagId}
                  className={styles.tag}
                  onClick={() => handleTagClick(tag)}
                >
                  {tag.tagName}
                </span>
              ))}
            </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
