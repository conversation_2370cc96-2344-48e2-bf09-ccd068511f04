.header {
  background: rgb(33, 37, 41);
  color: white;
  padding: 10px;
  position: sticky;
  top: 0;
  z-index: 1000;

  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
  }
}

.logo {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.logoImage {
  height: 30px;
  width: auto;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.searchBox {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.searchInput {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  height: 36px;
  box-sizing: border-box;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    background: rgba(255, 255, 255, 0.2);
  }
}

.searchButton {
  padding: 8px 16px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 36px;
  box-sizing: border-box;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.hotTags {
  width: 100%;
  display: flex;
  justify-content: flex-start;

  >span {
    width: 62px;
    font-size: 14px;
    color: #fff;
  }
}

.tagList {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  color: #ccc;
  font-size: 14px;
  cursor: pointer;
}

.loading {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-style: italic;
}

.actions {
  display: flex;
  align-items: center;
}

.latestAlbumBtn {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(238, 90, 36, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(238, 90, 36, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}