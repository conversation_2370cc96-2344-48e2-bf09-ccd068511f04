import React, { useState } from 'react';
import { useRouteCache } from '@/utils/routeCache';
import styles from './cacheManager.module.scss';

interface CacheManagerProps {
  className?: string;
}

const CacheManager: React.FC<CacheManagerProps> = ({ className }) => {
  const { state, clearCache } = useRouteCache();
  const [isOpen, setIsOpen] = useState(false);

  const cacheEntries = Object.entries(state);
  const totalCacheSize = cacheEntries.length;

  const handleClearAll = () => {
    if (window.confirm('确定要清除所有缓存吗？')) {
      clearCache();
    }
  };

  const handleClearRoute = (routeKey: string) => {
    if (window.confirm(`确定要清除路由 "${routeKey}" 的缓存吗？`)) {
      clearCache(routeKey);
    }
  };

  return (
    <div className={`${styles.cacheManager} ${className || ''}`}>
      {/* 缓存状态指示器 */}
      <div 
        className={styles.cacheIndicator}
        onClick={() => setIsOpen(!isOpen)}
        title="缓存管理"
      >
        <span className={styles.cacheIcon}>💾</span>
        <span className={styles.cacheCount}>{totalCacheSize}</span>
      </div>

      {/* 缓存详情面板 */}
      {isOpen && (
        <div className={styles.cachePanel}>
          <div className={styles.cacheHeader}>
            <h3>缓存管理</h3>
            <button 
              className={styles.closeBtn}
              onClick={() => setIsOpen(false)}
            >
              ✕
            </button>
          </div>

          <div className={styles.cacheStats}>
            <div className={styles.statItem}>
              <span className={styles.statLabel}>缓存条目:</span>
              <span className={styles.statValue}>{totalCacheSize}</span>
            </div>
          </div>

          <div className={styles.cacheActions}>
            <button 
              className={styles.clearAllBtn}
              onClick={handleClearAll}
            >
              清除所有缓存
            </button>
          </div>

          <div className={styles.cacheList}>
            <h4>缓存条目</h4>
            {cacheEntries.length === 0 ? (
              <p className={styles.noCache}>暂无缓存</p>
            ) : (
              <div className={styles.cacheItems}>
                {cacheEntries.map(([routeKey, cacheData]) => (
                  <div key={routeKey} className={styles.cacheItem}>
                    <div className={styles.cacheInfo}>
                      <div className={styles.routeKey}>{routeKey}</div>
                      <div className={styles.cacheTime}>
                        创建时间: {new Date(cacheData.timestamp).toLocaleString()}
                      </div>
                      {cacheData.scrollPosition && (
                        <div className={styles.scrollInfo}>
                          滚动位置: ({cacheData.scrollPosition.x}, {cacheData.scrollPosition.y})
                        </div>
                      )}
                    </div>
                    <button 
                      className={styles.clearRouteBtn}
                      onClick={() => handleClearRoute(routeKey)}
                    >
                      清除
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheManager;
