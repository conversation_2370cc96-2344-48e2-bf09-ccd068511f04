.cacheManager {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.cacheIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  .cacheIcon {
    font-size: 20px;
    color: white;
  }

  .cacheCount {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid white;
  }
}

.cachePanel {
  position: absolute;
  bottom: 60px;
  right: 0;
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideIn 0.3s ease;

  .cacheHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .closeBtn {
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .cacheStats {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #f8f9fa;

    .statItem {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .statLabel {
        color: #666;
        font-size: 14px;
      }

      .statValue {
        font-weight: 600;
        color: #333;
      }
    }
  }

  .cacheActions {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;

    .clearAllBtn {
      width: 100%;
      padding: 10px 16px;
      background: #ff4757;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #ff3742;
      }
    }
  }

  .cacheList {
    max-height: 300px;
    overflow-y: auto;

    h4 {
      margin: 0;
      padding: 16px 20px 8px;
      font-size: 14px;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
    }

    .noCache {
      padding: 20px;
      text-align: center;
      color: #999;
      font-style: italic;
    }

    .cacheItems {
      .cacheItem {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 12px 20px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .cacheInfo {
          flex: 1;
          margin-right: 12px;

          .routeKey {
            font-weight: 600;
            color: #333;
            font-size: 14px;
            margin-bottom: 4px;
            word-break: break-all;
          }

          .cacheTime {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
          }

          .scrollInfo {
            font-size: 12px;
            color: #888;
            font-family: monospace;
          }
        }

        .clearRouteBtn {
          padding: 4px 8px;
          background: #ff6b6b;
          color: white;
          border: none;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          transition: background-color 0.2s;
          white-space: nowrap;

          &:hover {
            background: #ff5252;
          }
        }
      }
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 480px) {
  .cacheManager {
    bottom: 10px;
    right: 10px;
  }

  .cachePanel {
    width: 300px;
    right: -25px;
  }
}
