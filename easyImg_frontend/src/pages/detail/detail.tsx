import React, { useEffect, useState } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import styles from './detail.module.scss';
import { getImgDetail, ImgDetail } from '@/remote/imgDetail';
import { useScrollToBottom } from '@/utils/useScrollToBottom';

const Detail: React.FC = () => {
  const { id } = useParams<{ id: string }>(); 
  const navigate = useNavigate();
  const [imgDetail, setImgDetail] = useState<ImgDetail>({
    imgUrlList: [],
    total: 0,
    title: '',
  });
  const [visibleImages, setVisibleImages] = useState<number>(1990); // 初始显示10张图片

  // 加载更多图片（懒加载）
  const loadMoreImages = () => {
    if (visibleImages >= imgDetail.imgUrlList.length) return;
    setVisibleImages(prev => Math.min(prev + 10, imgDetail.imgUrlList.length));
  };

  // 使用自定义Hook检测滚动到底部
  useScrollToBottom({
    threshold: 200,
    enabled: visibleImages < imgDetail.imgUrlList.length,
    onScrollToBottom: loadMoreImages,
  });

  useEffect(() => {
    getImgDetail(Number(id)).then(res => {
      setImgDetail(res);
      setVisibleImages(Math.min(100000, res.imgUrlList.length)); // 重置可见图片数量
    });
  }, [id]);

  return (
    <div className={styles.detailPage}>
      <div className={styles.detailHeader}>


        <span className={styles.backLink} onClick={() => navigate(-1)}>←</span>
        <h1>{imgDetail.title}</h1>
      </div>
      <div className={styles.detailContent}>
        {imgDetail.imgUrlList.slice(0, visibleImages).map((item, index) => (
          <img 
            src={item} 
            alt={`${imgDetail.title}-${index}`} 
            key={index}
            loading="lazy" // 添加懒加载属性
          />
        ))}
      </div>
      {visibleImages >= imgDetail.imgUrlList.length && imgDetail.imgUrlList.length > 0 && (
        <div className={styles.noMore}>
          <p>已显示全部图片</p>
        </div>
      )}
    </div>
  );
};

export default Detail;
