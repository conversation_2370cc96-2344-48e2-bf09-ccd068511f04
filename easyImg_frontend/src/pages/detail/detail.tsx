import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styles from './detail.module.scss';
import { getImgDetail, ImgDetail } from '@/remote/imgDetail';
import ImageWithPlaceholder from '@/components/loading/ImageWithPlaceholder';

const Detail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [imgDetail, setImgDetail] = useState<ImgDetail>({
    imgUrlList: [],
    total: 0,
    title: '',
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    getImgDetail(Number(id)).then(res => {
      setImgDetail(res);
      setLoading(false);
    });
  }, [id]);

  return (
    <div className={styles.detailPage}>
      <div className={styles.detailHeader}>
        <span className={styles.backLink} onClick={() => navigate(-1)}>←</span>
        <h1>{imgDetail.title}</h1>
      </div>
      <div className={styles.detailContent}>
        {imgDetail.imgUrlList.map((item, index) => (
          <ImageWithPlaceholder
            key={index}
            src={item}
            alt={`${imgDetail.title}-${index}`}
            placeholderText="加载中..."
          />
        ))}
      </div>
      {!loading && imgDetail.imgUrlList.length === 0 && <div className={styles.noMore}>
        <p>已显示全部图片</p>
      </div>}
    </div>
  );
};

export default Detail;
