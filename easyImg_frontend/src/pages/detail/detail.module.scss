/* 详情页面样式 - 移动端适配 */
.detailPage {
  padding: 16px;
  min-height: 100vh;
  background: #f5f5f5;

  p {
    color: #666;
    margin-bottom: 16px;
    font-size: 14px;
  }
}

.detailHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  position: sticky;
  top: 0;
  z-index: 100;
  background: #f5f5f5;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
}

.backLink {
  text-decoration: none;
  color: #007bff;
  padding: 8px;
  font-size: 14px;
}

.detailPage h1 {
  color: #333;
  margin: 0;
  text-align: left;
  flex-grow: 1;
  font-size: 16px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.detailContent {
  display: flex;
  flex-direction: column;
  gap: 5px;

  h2 {
    color: #333;
    margin-bottom: 12px;
    font-size: 18px;
    font-weight: 600;
  }

  p {
    color: #666;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
  }

  img {
    width: 100%;
    height: auto;
    border-radius: 8px;
  }
}

.projectInfo {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h3 {
    color: #333;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    color: #666;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:last-child {
      border-bottom: none;
    }

    &::before {
      content: attr(data-label);
      font-weight: 500;
      color: #333;
    }
  }
}

/* 加载状态样式 */
.loading {
  text-align: center;
  padding: 20px;
  margin-top: 20px;
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

/* 无更多数据样式 */
.noMore {
  text-align: center;
  padding: 20px;
  margin-top: 20px;
  
  p {
    margin: 0;
    color: #999;
    font-size: 14px;
  }
}
