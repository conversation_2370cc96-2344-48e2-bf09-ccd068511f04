import React, { useEffect, useState, useContext, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import styles from './search.module.scss';
import { getSearch, getSearchId } from '@/remote/search';
import type { albumType } from '@/remote/imgList/all';
import { useScrollToBottom } from '@/utils/useScrollToBottom';
import ImgList from '@/components/imgList/imgList';
import Loading from '@/components/loading/loading';
import { CacheContext } from '@/components/cacheComp/cacheComp';

const Search: React.FC = () => {
    const { keyword } = useParams<{ keyword: string }>();
    // 本地状态
    const [searchId, setSearchId] = useState<string>('');
    const [searchIdLoading, setSearchIdLoading] = useState(false);
    const [list, setList] = useState<albumType['list']>([]);
    const [hasMore, setHasMore] = useState(true);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const { cache, getCache, setCache } = useContext(CacheContext);

    const cacheKey = `/search/${keyword}`;


    useEffect(() => {
        const fetchSearchId = async () => {
            setSearchIdLoading(true);
            const data = await getSearchId(keyword || '');
            setSearchIdLoading(false);
            setSearchId(data.searchId);
            if (!data.searchId) {
                return;
            }

            // 处理缓存逻辑
            const cache = getCache(cacheKey)

            if (!cache) {
                setCache(cacheKey, {
                    list: [],
                    hasMore: true,
                    page: 1,
                    scrollTop: 0,
                    searchId: data.searchId,
                });
            } else {
                setList(cache.list);
                setHasMore(cache.hasMore);
                setPage(cache.page);

                requestAnimationFrame(() => {
                    window.scrollTo({
                        top: cache.scrollTop,
                        behavior: 'instant'
                    });
                });
            }
        };
        fetchSearchId();
    }, [keyword]);

    useEffect(() => {
        if (searchId) {
            loadData(1, false);
        }
    }, [searchId]);

    // 构建缓存key

    useEffect(() => {

    }, [keyword]);

    // 手动加载数据
    const loadData = async (pageNum: number, append: boolean = false) => {
        if (loading || !keyword) return;

        setLoading(true);
        try {
            const data = await getSearch(searchId, pageNum);

            const newList = append ? [...list, ...data.list] : data.list;
            const newPage = pageNum + 1;

            // 更新本地状态
            setList(newList);
            setHasMore(data.hasMore);
            setPage(newPage);

            setCache(cacheKey, {
                list: newList,
                hasMore: data.hasMore,
                page: newPage,
            });
        } catch (error) {
            console.error('搜索数据失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 加载更多数据
    const loadMore = () => {
        if (!loading && hasMore) {
            loadData(page, true);
        }
    };

    // 使用自定义Hook检测滚动到底部
    useScrollToBottom({
        threshold: 100,
        enabled: hasMore && !loading,
        onScrollToBottom: loadMore,
        onScroll: (e: any) => {
            // 获取滚动位置
            const scrollTop = e.target?.scrollingElement?.scrollTop;
            const currentCache = getCache(cacheKey);
            if (currentCache) {
                setCache(cacheKey, {
                    ...currentCache,
                    scrollTop: scrollTop,
                });
            }
        },
    });

    return (
        <div className={styles.searchPage}>
            <div className={styles.searchHeader}>
                <h2>搜索结果: {keyword}</h2>
            </div>
            {!searchIdLoading && !searchId && <span>没有找到相关图片</span>}
            <ImgList imgList={list} />
            <Loading
                loading={loading}
                hasMore={hasMore}
                currentLength={list.length}
            />
        </div>
    );
};

export default Search;
