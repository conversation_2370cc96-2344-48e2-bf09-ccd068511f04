import React, { useEffect, useState, useContext, useMemo } from 'react';
import styles from './newList.module.scss';
import { getImgList } from '@/remote/imgList/all';
import type{ albumType } from '@/remote/imgList/all';
import { useScrollToBottom } from '@/utils/useScrollToBottom';
import ImgList from '@/components/imgList/imgList';
import Loading from '@/components/loading/loading';
import { CacheContext } from '@/components/cacheComp/cacheComp';
import { useLocation } from 'react-router-dom';

const List: React.FC = () => {
  // 获取当前路径
  const pathname = useLocation().pathname;

  // 本地状态
  const [list, setList] = useState<albumType['list']>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const {cache, getCache, setCache } = useContext(CacheContext);
  

  useEffect(() => {
    // 处理缓存逻辑
    const cache = getCache(pathname)
    
    if (!cache) {
      setCache(pathname, {
        list: [],
        hasMore: true,
        page: 1,
        scrollTop: 0,
      });
      loadData(1, false);
    } else {
      setList(cache.list);
      setHasMore(cache.hasMore);
      setPage(cache.page);

      requestAnimationFrame(() => {
          window.scrollTo({
            top: cache.scrollTop,
            behavior: 'instant'
          });
      });
    }
  }, []);
  // 手动加载数据
  const loadData = async (pageNum: number, append: boolean = false) => {
    if (loading) return;
    
    setLoading(true);
    try {
      const data = await getImgList(pageNum);
      
      const newList = append ? [...list, ...data.list] : data.list;
      const newPage = pageNum + 1;
      
      // 更新本地状态
      setList(newList);
      setHasMore(data.hasMore);
      setPage(newPage);

      setCache(pathname, {
        list: newList,
        hasMore: data.hasMore,
        page: newPage,
      });
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载更多数据
  const loadMore = () => {
    if (!loading && hasMore) {
      loadData(page, true);
    }
  };

  // 使用自定义Hook检测滚动到底部
  useScrollToBottom({
    threshold: 100,
    enabled: hasMore && !loading,
    onScrollToBottom: loadMore,
    onScroll: (e: any) => {
      // 获取滚动位置
      const scrollTop = e.target?.scrollingElement?.scrollTop;
      const currentCache = getCache(pathname);
      if (currentCache) {
        setCache(pathname, {
          ...currentCache,
          scrollTop: scrollTop,
        });
      }
    },
  });

  return (
    <div className={styles.listPage}>
      <ImgList imgList={list} />
      <Loading 
        loading={loading} 
        hasMore={hasMore} 
        currentLength={list.length} 
      />
    </div>
  );
};

export default List;
