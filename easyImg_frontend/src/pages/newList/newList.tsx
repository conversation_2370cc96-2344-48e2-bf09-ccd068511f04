import React, { useEffect, useState } from 'react';
import styles from './newList.module.scss';
import { getImgList } from '@/remote/imgList/all';
import type{ albumType } from '@/remote/imgList/all';
import { useScrollToBottom } from '@/utils/useScrollToBottom';
import { useManualCache, useScrollCache } from '@/utils/usePageCache';
import ImgList from '@/components/imgList/imgList';
import Loading from '@/components/loading/loading';

const List: React.FC = () => {
  // 使用手动缓存Hook
  const { setCacheData, getCacheData, clearCacheData } = useManualCache<{
    list: albumType['list'];
    hasMore: boolean;
    page: number;
  }>();

  // 本地状态
  const [list, setList] = useState<albumType['list']>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);

  // 手动加载数据
  const loadData = async (pageNum: number, append: boolean = false) => {
    if (loading) return;
    
    setLoading(true);
    try {
      const data = await getImgList(pageNum);
      
      const newList = append ? [...list, ...data.list] : data.list;
      const newPage = pageNum + 1;
      
      // 更新本地状态
      setList(newList);
      setHasMore(data.hasMore);
      setPage(newPage);
      
      // 更新缓存
      setCacheData({
        list: newList,
        hasMore: data.hasMore,
        page: newPage,
      });
      
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载更多数据
  const loadMore = () => {
    if (!loading && hasMore) {
      loadData(page, true);
    }
  };

  // 初始加载数据
  useEffect(() => {
    // 尝试从缓存恢复数据
    const cachedData = getCacheData();
    if (cachedData && cachedData.list.length > 0) {
      setList(cachedData.list);
      setHasMore(cachedData.hasMore);
      setPage(cachedData.page);
    } else {
      // 缓存中没有数据，加载第一页
      loadData(1, false);
    }
  }, []);

  // 使用滚动位置缓存
  useScrollCache({
    autoRestore: true,
    autoSave: true,
  });

  // 使用自定义Hook检测滚动到底部
  useScrollToBottom({
    threshold: 100,
    enabled: hasMore && !loading,
    onScrollToBottom: loadMore,
  });

  return (
    <div className={styles.listPage}>
      <ImgList imgList={list} />
      <Loading 
        loading={loading} 
        hasMore={hasMore} 
        currentLength={list.length} 
      />
    </div>
  );
};

export default List;
