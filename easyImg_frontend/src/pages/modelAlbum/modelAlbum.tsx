import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import styles from './modelAlbum.module.scss';
import { getImgList } from '@/remote/imgList/getImgListByType';
import type { albumType } from '@/remote/imgList/all';
import { useScrollToBottom } from '@/utils/useScrollToBottom';
import { useManualCache, useScrollCache } from '@/utils/usePageCache';
import ImgList from '@/components/imgList/imgList';
import Loading from '@/components/loading/loading';

const ModelAlbum: React.FC = () => {
  const { tagId } = useParams<{ tagId: string }>();
  const tagIdValue = tagId || '';
  
  // 使用手动缓存Hook，将tagId作为缓存键的一部分
  const { setCacheData, getCacheData, clearCacheData } = useManualCache<{
    list: albumType['list'];
    hasMore: boolean;
    page: number;
  }>(`modelAlbum-${tagIdValue}`);

  // 本地状态
  const [list, setList] = useState<albumType['list']>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);

  // 手动加载数据
  const loadData = async (pageNum: number, append: boolean = false) => {
    if (loading || !tagIdValue) return;
    
    setLoading(true);
    try {
      const data = await getImgList(tagIdValue, pageNum);
      
      const newList = append ? [...list, ...data.list] : data.list;
      const newPage = pageNum + 1;
      
      // 更新本地状态
      setList(newList);
      setHasMore(data.hasMore);
      setPage(newPage);
      
      // 更新缓存
      setCacheData({
        list: newList,
        hasMore: data.hasMore,
        page: newPage,
      });
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载更多数据
  const loadMore = () => {
    if (!loading && hasMore && tagIdValue) {
      loadData(page, true);
    }
  };

  // 使用滚动位置缓存
  useScrollCache({
    autoRestore: true,
    autoSave: true,
  });

  // 使用自定义Hook检测滚动到底部
  useScrollToBottom({
    threshold: 100,
    enabled: hasMore && !loading && !!tagIdValue,
    onScrollToBottom: loadMore,
  });

  // 初始加载数据
  useEffect(() => {
    if (tagIdValue) {
      // 尝试从缓存恢复数据
      const cachedData = getCacheData();
      if (cachedData && cachedData.list.length > 0) {
        setList(cachedData.list);
        setHasMore(cachedData.hasMore);
        setPage(cachedData.page);
      } else {
        // 缓存中没有数据，加载第一页
        setPage(1);
        loadData(1, false);
      }
    }
  }, [tagIdValue]);

  return (
    <div className={styles.modelAlbumPage}>
      <ImgList imgList={list} />
      <Loading loading={loading} hasMore={hasMore} currentLength={list.length} />
    </div>
  );
};

export default ModelAlbum;