import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import List from './pages/newList/newList';
import Detail from './pages/detail/detail';
import ModelAlbum from './pages/modelAlbum/modelAlbum';
import TagAlbum from './pages/tagAlbum/tagAlbum';
import { CacheProvider } from './utils/routeCache';
import CacheManager from './components/cacheManager/cacheManager';
import Header from './components/header/header';

const App: React.FC = () => {
  return (
    <CacheProvider maxAge={30 * 60 * 1000} maxSize={50}>
      <Router>
        <div className="App">
          <Header />
          <Routes>
            {/* 空路由重定向到 /list */}
            <Route path="/" element={<Navigate to="/newList" replace />} />
            {/* 列表页面路由 */}
            <Route path="/newList" element={<List />} />
            {/* 详情页面路由，支持动态id参数 */}
            <Route path="/detail/:id" element={<Detail />} />
            <Route path="/modelAlbum/:tagId" element={<ModelAlbum />} />
            <Route path="/tagAlbum/:tagId" element={<TagAlbum />} />
          </Routes>
          
          {/* 缓存管理组件 */}
          <CacheManager />
        </div>
      </Router>
    </CacheProvider>
  );
};

export default App;
