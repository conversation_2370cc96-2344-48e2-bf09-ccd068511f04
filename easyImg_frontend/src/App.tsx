import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import List from './pages/newList/newList';
import Detail from './pages/detail/detail';
import ModelAlbum from './pages/modelAlbum/modelAlbum';
import TagAlbum from './pages/tagAlbum/tagAlbum';
import Search from './pages/search/search';
import Header from './components/header/header';
import { CacheProvider } from './components/cacheComp/cacheComp';
import BackToTop from './components/backToTop/backToTop';

const App: React.FC = () => {
  return (
    <CacheProvider maxCacheSize={10}>
      <Router>
        <div className="App">
          <Header />
          <Routes>
            {/* 空路由重定向到 /list */}
            <Route path="/" element={<Navigate to="/newList" replace />} />
            {/* 列表页面路由 */}
            <Route path="/newList" element={<List />} />
            {/* 详情页面路由，支持动态id参数 */}
            <Route path="/detail/:id" element={<Detail />} />
            <Route path="/modelAlbum/:tagId" element={<ModelAlbum />} />
            <Route path="/tagAlbum/:tagId" element={<TagAlbum />} />
            <Route path="/search/:keyword" element={<Search />} />
          </Routes>
          <BackToTop />
        </div>
      </Router>
    </CacheProvider>
  );
};

export default App;
