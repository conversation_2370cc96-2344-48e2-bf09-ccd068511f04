import { useState, useEffect, useCallback } from 'react';
import { useRouteCache, useRouteKey } from './routeCache';

// 页面缓存Hook的配置选项
interface UsePageCacheOptions {
  cacheKey?: string;
  dependencies?: any[];
  autoRestore?: boolean; // 是否自动恢复状态
  autoSave?: boolean; // 是否自动保存状态
  saveInterval?: number; // 自动保存间隔（毫秒）
}

// 页面缓存Hook
export function usePageCache<T>(
  initialState: T,
  options: UsePageCacheOptions = {}
): [T, (newState: T | ((prev: T) => T)) => void, () => void] {
  const {
    cacheKey,
    dependencies = [],
    autoRestore = true,
    autoSave = true,
    saveInterval = 5000, // 5秒
  } = options;

  const { getCache, setCache, clearCache } = useRouteCache();
  const routeKey = useRouteKey();
  const finalCacheKey = cacheKey || routeKey;

  const [state, setState] = useState<T>(() => {
    // 初始化时尝试从缓存恢复状态
    if (autoRestore) {
      const cached = getCache(finalCacheKey);
      if (cached?.state) {
        return cached.state;
      }
    }
    return initialState;
  });

  // 保存状态到缓存
  const saveToCache = useCallback((currentState: T) => {
    setCache(finalCacheKey, {
      state: currentState,
      dependencies,
      timestamp: Date.now(),
    });
  }, [setCache, finalCacheKey, dependencies]);

  // 从缓存恢复状态
  const restoreFromCache = useCallback(() => {
    const cached = getCache(finalCacheKey);
    if (cached?.state) {
      setState(cached.state);
      return true;
    }
    return false;
  }, [getCache, finalCacheKey]);

  // 清除缓存
  const clearPageCache = useCallback(() => {
    clearCache(finalCacheKey);
  }, [clearCache, finalCacheKey]);

  // 状态更新函数
  const updateState = useCallback((newState: T | ((prev: T) => T)) => {
    setState(prevState => {
      const nextState = typeof newState === 'function' ? (newState as (prev: T) => T)(prevState) : newState;
      
      // 自动保存到缓存
      if (autoSave) {
        saveToCache(nextState);
      }
      
      return nextState;
    });
  }, [autoSave, saveToCache]);

  // 依赖项变化时重新加载
  useEffect(() => {
    if (dependencies.length > 0) {
      const cached = getCache(finalCacheKey);
      if (cached?.dependencies) {
        const depsChanged = dependencies.length !== cached.dependencies.length ||
          dependencies.some((dep, index) => dep !== cached.dependencies[index]);
        
        if (depsChanged) {
          // 依赖项变化，清除缓存并重置状态
          clearPageCache();
          setState(initialState);
        }
      }
    }
  }, [dependencies, finalCacheKey, getCache, clearPageCache, initialState]);

  // 自动保存定时器
  useEffect(() => {
    if (autoSave && saveInterval > 0) {
      const interval = setInterval(() => {
        saveToCache(state);
      }, saveInterval);

      return () => clearInterval(interval);
    }
  }, [autoSave, saveInterval, saveToCache, state]);

  // 页面卸载时保存状态
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (autoSave) {
        saveToCache(state);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [autoSave, saveToCache, state]);

  return [state, updateState, clearPageCache];
}

// 手动更新缓存的Hook
export function useManualCache<T>(cacheKey?: string) {
  const { setCache, getCache, clearCache } = useRouteCache();
  const routeKey = useRouteKey();
  const finalCacheKey = cacheKey || routeKey;

  // 手动设置缓存
  const setCacheData = useCallback((data: T) => {
    setCache(finalCacheKey, {
      state: data,
      timestamp: Date.now(),
    });
  }, [setCache, finalCacheKey]);

  // 手动获取缓存
  const getCacheData = useCallback((): T | null => {
    const cached = getCache(finalCacheKey);
    return cached?.state || null;
  }, [getCache, finalCacheKey]);

  // 手动清除缓存
  const clearCacheData = useCallback(() => {
    clearCache(finalCacheKey);
  }, [clearCache, finalCacheKey]);

  return {
    setCacheData,
    getCacheData,
    clearCacheData,
    cacheKey: finalCacheKey,
  };
}

// 滚动位置缓存Hook
export function useScrollCache(options: UsePageCacheOptions = {}) {
  const { cacheKey, autoRestore = true, autoSave = true } = options;
  const { getCache, setCache, getScrollPosition, setScrollPosition } = useRouteCache();
  const routeKey = useRouteKey();
  const finalCacheKey = cacheKey || routeKey;

  // 保存滚动位置
  const saveScrollPosition = useCallback(() => {
    const position = {
      x: window.scrollX,
      y: window.scrollY,
    };
    setScrollPosition(finalCacheKey, position);
  }, [setScrollPosition, finalCacheKey]);

  // 恢复滚动位置
  const restoreScrollPosition = useCallback(() => {
    const position = getScrollPosition(finalCacheKey);
    if (position) {
      window.scrollTo(position.x, position.y);
      return true;
    }
    return false;
  }, [getScrollPosition, finalCacheKey]);

  // 自动保存滚动位置
  useEffect(() => {
    if (!autoSave) return;

    const handleScroll = () => {
      saveScrollPosition();
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [autoSave, saveScrollPosition]);

  // 自动恢复滚动位置
  useEffect(() => {
    if (autoRestore) {
      // 延迟恢复，确保页面渲染完成
      const timer = setTimeout(() => {
        restoreScrollPosition();
      }, 10);

      return () => clearTimeout(timer);
    }
  }, [autoRestore, restoreScrollPosition]);

  return {
    saveScrollPosition,
    restoreScrollPosition,
  };
}

// 列表数据缓存Hook
export function useListCache<T>(
  fetchFunction: (page: number) => Promise<{ list: T[]; hasMore: boolean }>,
  options: UsePageCacheOptions = {}
) {
  const [listData, setListData, clearListCache] = usePageCache<{
    list: T[];
    hasMore: boolean;
    page: number;
    loading: boolean;
  }>({
    list: [],
    hasMore: true,
    page: 1,
    loading: false,
  }, options);

  const loadMore = useCallback(async () => {
    if (listData.loading || !listData.hasMore) return;

    setListData(prev => ({ ...prev, loading: true }));

    try {
      const data = await fetchFunction(listData.page);
      setListData(prev => ({
        list: [...prev.list, ...data.list],
        hasMore: data.hasMore,
        page: prev.page + 1,
        loading: false,
      }));
    } catch (error) {
      console.error('加载数据失败:', error);
      setListData(prev => ({ ...prev, loading: false }));
    }
  }, [fetchFunction, listData.loading, listData.hasMore, listData.page, setListData]);

  const refresh = useCallback(async () => {
    setListData(prev => ({ ...prev, loading: true, page: 1 }));
    
    try {
      const data = await fetchFunction(1);
      setListData({
        list: data.list,
        hasMore: data.hasMore,
        page: 2,
        loading: false,
      });
    } catch (error) {
      console.error('刷新数据失败:', error);
      setListData(prev => ({ ...prev, loading: false }));
    }
  }, [fetchFunction, setListData]);

  return {
    list: listData.list,
    hasMore: listData.hasMore,
    loading: listData.loading,
    loadMore,
    refresh,
    clearCache: clearListCache,
  };
}
