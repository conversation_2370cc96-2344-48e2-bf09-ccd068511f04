import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useLocation } from 'react-router-dom';

// 缓存数据类型定义
interface CacheData {
  [key: string]: any;
}

// 缓存状态接口
interface CacheState {
  [routeKey: string]: {
    data: CacheData;
    timestamp: number;
    scrollPosition?: { x: number; y: number };
  };
}

// 缓存动作类型
type CacheAction =
  | { type: 'SET_CACHE'; routeKey: string; data: CacheData }
  | { type: 'CLEAR_CACHE'; routeKey?: string }
  | { type: 'SET_SCROLL_POSITION'; routeKey: string; position: { x: number; y: number } }
  | { type: 'CLEAR_EXPIRED_CACHE'; maxAge: number };

// 缓存上下文
interface CacheContextType {
  state: CacheState;
  setCache: (routeKey: string, data: CacheData) => void;
  getCache: (routeKey: string) => CacheData | null;
  clearCache: (routeKey?: string) => void;
  setScrollPosition: (routeKey: string, position: { x: number; y: number }) => void;
  getScrollPosition: (routeKey: string) => { x: number; y: number } | null;
  isCached: (routeKey: string) => boolean;
}

const CacheContext = createContext<CacheContextType | undefined>(undefined);

// 缓存Reducer
const cacheReducer = (state: CacheState, action: CacheAction): CacheState => {
  switch (action.type) {
    case 'SET_CACHE':
      return {
        ...state,
        [action.routeKey]: {
          data: action.data,
          timestamp: Date.now(),
          scrollPosition: state[action.routeKey]?.scrollPosition,
        },
      };
    
    case 'CLEAR_CACHE':
      if (action.routeKey) {
        const newState = { ...state };
        delete newState[action.routeKey];
        return newState;
      }
      return {};
    
    case 'SET_SCROLL_POSITION':
      return {
        ...state,
        [action.routeKey]: {
          ...state[action.routeKey],
          scrollPosition: action.position,
        },
      };
    
    case 'CLEAR_EXPIRED_CACHE':
      const now = Date.now();
      const newState = { ...state };
      Object.keys(newState).forEach(key => {
        if (now - newState[key].timestamp > action.maxAge) {
          delete newState[key];
        }
      });
      return newState;
    
    default:
      return state;
  }
};

// 缓存Provider组件
interface CacheProviderProps {
  children: ReactNode;
  maxAge?: number; // 缓存过期时间（毫秒），默认30分钟
  maxSize?: number; // 最大缓存条目数，默认50个
}

export const CacheProvider: React.FC<CacheProviderProps> = ({
  children,
  maxAge = 30 * 60 * 1000, // 30分钟
  maxSize = 50,
}) => {
  const [state, dispatch] = useReducer(cacheReducer, {});

  // 定期清理过期缓存
  useEffect(() => {
    const interval = setInterval(() => {
      dispatch({ type: 'CLEAR_EXPIRED_CACHE', maxAge });
    }, 60000); // 每分钟检查一次

    return () => clearInterval(interval);
  }, [maxAge]);

  // 控制缓存大小
  useEffect(() => {
    const cacheKeys = Object.keys(state);
    if (cacheKeys.length > maxSize) {
      // 删除最旧的缓存
      const sortedKeys = cacheKeys.sort(
        (a, b) => state[a].timestamp - state[b].timestamp
      );
      const keysToRemove = sortedKeys.slice(0, cacheKeys.length - maxSize);
      keysToRemove.forEach(key => {
        dispatch({ type: 'CLEAR_CACHE', routeKey: key });
      });
    }
  }, [state, maxSize]);

  const setCache = (routeKey: string, data: CacheData) => {
    dispatch({ type: 'SET_CACHE', routeKey, data });
  };

  const getCache = (routeKey: string): CacheData | null => {
    const cache = state[routeKey];
    if (!cache) return null;
    
    // 检查是否过期
    if (Date.now() - cache.timestamp > maxAge) {
      dispatch({ type: 'CLEAR_CACHE', routeKey });
      return null;
    }
    
    return cache.data;
  };

  const clearCache = (routeKey?: string) => {
    dispatch({ type: 'CLEAR_CACHE', routeKey });
  };

  const setScrollPosition = (routeKey: string, position: { x: number; y: number }) => {
    dispatch({ type: 'SET_SCROLL_POSITION', routeKey, position });
  };

  const getScrollPosition = (routeKey: string) => {
    return state[routeKey]?.scrollPosition || null;
  };

  const isCached = (routeKey: string): boolean => {
    return getCache(routeKey) !== null;
  };

  const value: CacheContextType = {
    state,
    setCache,
    getCache,
    clearCache,
    setScrollPosition,
    getScrollPosition,
    isCached,
  };

  return <CacheContext.Provider value={value}>{children}</CacheContext.Provider>;
};

// 使用缓存的Hook
export const useRouteCache = () => {
  const context = useContext(CacheContext);
  if (!context) {
    throw new Error('useRouteCache must be used within a CacheProvider');
  }
  return context;
};

// 生成路由键的Hook
export const useRouteKey = () => {
  const location = useLocation();
  return location.pathname + location.search;
};