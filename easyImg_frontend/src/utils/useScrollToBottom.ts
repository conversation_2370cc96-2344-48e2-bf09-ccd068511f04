import { useEffect, useCallback, useRef } from 'react';

interface UseScrollToBottomOptions {
  threshold?: number; // 距离底部的阈值，默认100px
  enabled?: boolean; // 是否启用检测
  onScrollToBottom?: () => void; // 滚动到底部时的回调
}

export const useScrollToBottom = ({
  threshold = 100,
  enabled = true,
  onScrollToBottom,
}: UseScrollToBottomOptions = {}) => {
  const isScrolling = useRef(false);

  const handleScroll = useCallback(() => {
    if (!enabled || !onScrollToBottom || isScrolling.current) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    
    // 当滚动到距离底部指定阈值时触发回调
    if (scrollTop + windowHeight >= documentHeight - threshold) {
      isScrolling.current = true;
      onScrollToBottom();
      
      // 防止短时间内重复触发
      setTimeout(() => {
        isScrolling.current = false;
      }, 500);
    }
  }, [enabled, onScrollToBottom, threshold]);

  useEffect(() => {
    if (!enabled) return;
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll, enabled]);

  // 手动触发滚动检测
  const checkScroll = useCallback(() => {
    handleScroll();
  }, [handleScroll]);

  return {
    checkScroll,
  };
};
