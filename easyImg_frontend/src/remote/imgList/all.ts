import request from '@/utils/request';

export type TagItem = {
  tagName: string;
  tagId: string;
}

export type ImgListType = {
    coverImg: string;
    title: string;
    id: number;
    modelList: TagItem[];
    tagList: TagItem[];
};

export type albumType = {
    list: ImgListType[];
    hasMore: boolean;
}

export const getImgList = async (page: number): Promise<albumType> => {
  const res = await request.get<albumType>(`/api/imgList/all`, {
    params: {
      page,
    },
  });
  return res;
};