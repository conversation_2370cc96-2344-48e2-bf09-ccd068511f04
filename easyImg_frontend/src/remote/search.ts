import request from '@/utils/request';
import type { albumType } from './imgList/all';

export const getSearch = async (searchId: string, page: number = 1): Promise<albumType> => {
    const res = await request.get<albumType>(`/search?searchId=${encodeURIComponent(searchId)}&page=${page}`);
    return res;
};
export const getSearchId = async (keyword: string): Promise<{ searchId: string }> => {
    const res = await request.get<{ searchId: string }>(`/search/getSearchId?keyword=${encodeURIComponent(keyword)}`);
    return res;
};