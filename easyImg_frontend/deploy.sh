#!/bin/bash

TARGET_PATH="/home/<USER>"
NEW_NAME="esayImg"
BUILD_DIR="build"
ARCHIVE_TYPE=".zip"
ARCHIVE_NAME=$BUILD_DIR$ARCHIVE_TYPE
SERVER_HOST="*************"
SERVER_USER="root"
SERVER_PATH="~"

echo $ARCHIVE_NAME

# 打包项目
echo "开始打包项目..."
npm run build

# 压缩打包结果
echo "压缩打包结果..."
zip -r $ARCHIVE_NAME $BUILD_DIR

# 上传到服务器
echo "上传到服务器..."
scp $ARCHIVE_NAME $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

# 登录服务器并执行操作
echo "在服务器上执行操作..."
ssh $SERVER_USER@$SERVER_HOST << EOF
    cd $SERVER_PATH
    unzip $ARCHIVE_NAME
    rm $ARCHIVE_NAME
    mkdir -p $TARGET_PATH
    mv $BUILD_DIR $TARGET_PATH/
    cd $TARGET_PATH
    rm -rf $NEW_NAME
    mv $BUILD_DIR $NEW_NAME
EOF

# 清理本地文件
echo "清理本地文件..."
rm $ARCHIVE_NAME

echo "部署完成！"
