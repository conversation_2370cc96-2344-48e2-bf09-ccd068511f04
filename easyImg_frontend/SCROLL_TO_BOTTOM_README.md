# 滚动到底部功能实现说明

## 功能概述

本项目已成功添加了滚动到底部的事件处理功能，包括：

1. **列表页面**：滚动到底部时加载更多数据
2. **详情页面**：滚动到底部时懒加载更多图片
3. **通用Hook**：可复用的滚动检测Hook

## 实现细节

### 1. 自定义Hook - useScrollToBottom

位置：`src/utils/useScrollToBottom.ts`

```typescript
interface UseScrollToBottomOptions {
  threshold?: number; // 距离底部的阈值，默认100px
  enabled?: boolean; // 是否启用检测
  onScrollToBottom?: () => void; // 滚动到底部时的回调
}
```

**特性：**
- 可配置触发阈值
- 防抖处理，避免重复触发
- 支持启用/禁用检测
- 使用passive事件监听器提升性能

### 2. 列表页面功能

位置：`src/pages/list/list.tsx`

**功能：**
- 滚动到底部时自动加载更多数据
- 显示加载状态
- 显示"没有更多数据"提示

**注意事项：**
- 当前API不支持分页，需要后端配合实现
- 已预留分页接口，等待后端支持

### 3. 详情页面功能

位置：`src/pages/detail/detail.tsx`

**功能：**
- 初始只显示10张图片
- 滚动到底部时加载更多图片
- 图片懒加载优化
- 显示加载状态和完成提示

## 使用方法

### 基本用法

```typescript
import { useScrollToBottom } from '@/utils/useScrollToBottom';

const MyComponent = () => {
  const handleScrollToBottom = () => {
    // 处理滚动到底部的逻辑
    console.log('滚动到底部了！');
  };

  useScrollToBottom({
    threshold: 100, // 距离底部100px时触发
    enabled: true, // 启用检测
    onScrollToBottom: handleScrollToBottom,
  });

  return <div>你的组件内容</div>;
};
```

### 高级用法

```typescript
const MyComponent = () => {
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const loadMore = async () => {
    if (loading || !hasMore) return;
    
    setLoading(true);
    try {
      // 加载数据的逻辑
      const newData = await fetchMoreData();
      // 处理新数据...
    } catch (error) {
      console.error('加载失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useScrollToBottom({
    threshold: 200,
    enabled: hasMore && !loading,
    onScrollToBottom: loadMore,
  });

  return <div>组件内容</div>;
};
```

## 样式说明

已添加的样式类：

- `.loading` - 加载状态样式
- `.noMore` - 无更多数据样式

这些样式已在 `list.module.scss` 和 `detail.module.scss` 中定义。

## 性能优化

1. **防抖处理**：避免短时间内重复触发
2. **Passive事件监听**：提升滚动性能
3. **条件检测**：只在需要时启用检测
4. **懒加载**：图片按需加载

## 后续改进建议

1. **后端分页支持**：实现真正的分页加载
2. **虚拟滚动**：对于大量数据考虑使用虚拟滚动
3. **缓存机制**：添加数据缓存提升用户体验
4. **错误处理**：完善错误重试机制

## 注意事项

1. 确保在组件卸载时正确清理事件监听器（Hook已自动处理）
2. 根据实际需求调整触发阈值
3. 考虑移动端的滚动性能优化
4. 测试不同设备和浏览器的兼容性
